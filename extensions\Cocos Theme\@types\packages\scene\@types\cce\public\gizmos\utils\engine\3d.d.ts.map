{"version": 3, "file": "3d.d.ts", "sourceRoot": "", "sources": ["../../../../../../source/script/public/gizmos/utils/engine/3d.ts"], "names": [], "mappings": "AAEA,OAAO,QAAQ,IAAI,CAAC;IAChB,UAAU,IAAI;QACV,SAAS,CAAC,EAAE,cAAc,CAAC;QAC3B,UAAU,CAAC,EAAE,KAAK,CAAC;KACtB;IACD,UAAU,gBAAgB;QACtB,OAAO,CAAC,EAAE,WAAW,CAAC;QACtB,OAAO,CAAC,EAAE,WAAW,CAAC;KACzB;CACJ;AAED,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,YAAY,EAAE,cAAc,EAAE,IAAI,EAAE,UAAU,EAA0D,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAyB,OAAO,EAAE,MAAM,IAAI,CAAC;AAErP,OAAO,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,cAAc,EAAE,oBAAoB,EAAE,MAAM,eAAe,CAAC;AAE9G,OAAO,eAAe,MAAM,oBAAoB,CAAC;AAqBjD,oBAAY,aAAa;IACrB,IAAI,IAAA;IACJ,EAAE,IAAA;IACF,IAAI,IAAA;IACJ,IAAI,IAAA;IACJ,KAAK,IAAA;IACL,KAAK,IAAA;IACL,IAAI,IAAA;CACP;AACD,qBAAa,QAAS,YAAW,eAAe;IAC5C,OAAc,QAAQ,EAAE,QAAQ,CAAC;WACnB,cAAc;IAG5B,SAAgB,aAAa,EAAE,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACrD,SAAgB,QAAQ,EAAE,QAAQ,CAAC,OAAO,GAAG,CAAC,QAAQ,CAAC,CAAC;IACxD,SAAgB,aAAa,EAAE,QAAQ,CAAC,OAAO,GAAG,CAAC,aAAa,CAAC,CAAC;IAClE,SAAgB,aAAa,EAAE,QAAQ,CAAC,OAAO,GAAG,CAAC,aAAa,CAAC,CAAC;IAClE,SAAgB,SAAS,EAAE,QAAQ,CAAC,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC;IACvD,SAAgB,cAAc,EAAE,QAAQ,CAAC,OAAO,MAAM,CAAC,cAAc,CAAC,CAAC;IACvE,SAAgB,OAAO,EAAE,QAAQ,CAAC,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC;IACzD,SAAS;IAWF,YAAY,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI;IAOjC,UAAU,CAAC,SAAS,EAAE,cAAc,EAAE,IAAI,GAAE,iBAAsB;IA+DlE,0BAA0B,CAAC,SAAS,EAAE,cAAc,GAAG,UAAU,CAAC,gBAAgB;IAUlF,iBAAiB,CAAC,SAAS,EAAE,oBAAoB,EAAE,IAAI,EAAE,CAAC,UAAU,CAAC,yBAAyB,GAAG,iBAAiB,CAAC,GAAG,IAAI;IA+D1H,iBAAiB,CAAC,YAAY,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,oBAAoB;IAK/F,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,GAAE,oBAAyB,EAAE,aAAa,CAAC,EAAE,QAAQ;IAuE9F,qBAAqB,CAAC,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,YAAY;IAmB5D,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK;IAUjC,YAAY,CAAC,IAAI,EAAE,IAAI;IAIvB,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM;IAO1C,cAAc,CAAC,IAAI,EAAE,IAAI;IAIzB,mBAAmB,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG;IAI5D,iBAAiB,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,SAAW,EAAE,WAAW,CAAC,EAAE,MAAM;IAajG,wBAAwB,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,oBAAW,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC,EAAE,MAAM;IA0BzH,OAAO,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,SAAW,EAAE,WAAW,CAAC,EAAE,MAAM;IAgB5G,mBAAmB,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,gBAAgB,EAAE,GAAG;QAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAA;KAAC;IAezG,QAAQ,CAAC,IAAI,EAAE,IAAI;IAInB,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE;IAoBrD,YAAY,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE;IAkCpE,QAAQ,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI;IA0C3C,iBAAiB,CAAC,QAAQ,EAAE,YAAY,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,IAAI;IAShF,cAAc,CAAC,SAAS,EAAE,SAAS;IA8BnC,eAAe,CAAC,SAAS,EAAE,GAAG;IAW9B,eAAe,CAAC,SAAS,EAAE,GAAG;IAmB9B,aAAa,CAAC,SAAS,EAAE,MAAM;IAqB/B,aAAa,CAAC,SAAS,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG;IAgB7C,YAAY,CAAC,SAAS,EAAE,GAAG;IAiB3B,YAAY,CAAC,SAAS,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG;CAcrD;;AAED,wBAAyC"}