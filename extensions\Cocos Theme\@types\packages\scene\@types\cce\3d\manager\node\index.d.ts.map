{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../../../../source/script/3d/manager/node/index.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,SAAS,EAAS,iBAAiB,EAAE,MAAM,8BAA8B,CAAC;AASnF,OAAO,YAAY,MAAM,8BAA8B,CAAC;AAgBxD,OAAO,EACH,IAAI,EAyBJ,SAAS,EAOZ,MAAM,IAAI,CAAC;AAGZ,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AA+BzD;;;;;;;;;;GAUG;AACH,qBAAa,WAAY,SAAQ,YAAa,YAAW,YAAY;IACjE,YAAY,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC;IACxC,cAAc,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC;IAC1C,cAAc,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC;IAC1C,mBAAmB,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC;IAC/C,cAAc,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC;IAC1C,gBAAgB,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC;IAC5C,gBAAgB,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC;IAC5C,oBAAoB,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC;IAEhD,OAAO,CAAC,UAAU,CAAmB;IACrC,OAAO,CAAC,sBAAsB,CAA4C;IAC1E,IAAI,mBAAmB,aAEtB;IAED,IAAI;IAEJ;;;OAGG;IACH,aAAa,CAAC,KAAK,EAAE,GAAG;IAqBjB,aAAa,CAAC,KAAK,EAAE,GAAG;IAIxB,aAAa;IAMpB;;OAEG;IACH,qBAAqB;IASrB;;OAEG;IACH,uBAAuB;IAYvB;;;OAGG;IACH,sBAAsB,CAAC,IAAI,EAAE,IAAI;IAoBjC;;;OAGG;IACH,wBAAwB,CAAC,IAAI,EAAE,IAAI;IAanC,sBAAsB,CAAC,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG;IAkBpD,iBAAiB,CAAC,IAAI,EAAE,IAAI;IAU5B,mBAAmB,CAAC,IAAI,EAAE,IAAI;IAU9B;;;;OAIG;IACH,mBAAmB,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI;IAa7C;;OAEG;IACH,mBAAmB,CAAC,IAAI,EAAE,IAAI;IAK9B;;OAEG;IACH,KAAK;IAUL;;;OAGG;IACH,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI;IAQ5B;;;;OAIG;IACH,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI;IAc/B;;;OAGG;IACH,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI;IAO/B;;;;OAIG;IACH,KAAK,CAAC,IAAI,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI,GAAG,IAAI;IAQ5C;;OAEG;IACH,UAAU;IAKV;;;;OAIG;IACH,SAAS,CAAC,IAAI,EAAE,MAAM;IAStB;;;;OAIG;IACH,cAAc,CAAC,IAAI,EAAE,MAAM;IAQ3B;;;OAGG;IACH,qBAAqB,CAAC,IAAI,EAAE,MAAM;IAQlC;;;;OAIG;IACH,mBAAmB;IAyBnB;;;;;;OAMG;IACG,sBAAsB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC;IAiCrF,4BAA4B,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAyBhF;;;;;;;OAOG;IACG,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,UAAO,GAAG,OAAO,CAAC,OAAO,CAAC;IAkC/F;;;;;OAKG;IACG,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAyBjE;;;;OAIG;IACG,sBAAsB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAyB1E;;;OAGG;IACG,SAAS,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IA4B/C;;;;OAIG;IACG,uBAAuB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;IAYrD;;;;;;OAMG;IACH,gBAAgB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO;IA6DrF;;;;;OAKG;IACH,kBAAkB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,OAAO;IAwDtE;;;;OAIG;IACH,eAAe,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,EAAE,EAAE,SAAS,EAAE,MAAM,GAAG,OAAO;IAyDpE;;;OAGG;IACG,cAAc,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAkBpD;;;OAGG;IACH,eAAe,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO;IAUtC;;;OAGG;IACH,QAAQ,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE;IA+DjC;;;;OAIG;IACH,aAAa,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE;IAmCtC;;;;;OAKG;IACH,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,EAAE,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,EAAE,kBAAkB,UAAQ;IAgCjG;;;;;OAKG;IACH,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,EAAE,kBAAkB,UAAQ;IA0B9E;;;;;OAKG;IACH,qBAAqB,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM;IAcvD;;;;;;OAMG;IACH,UAAU,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,EAAE,IAAI,EAAE,GAAG,EAAE,SAAS,EAAE,MAAM,GAAG,IAAI,EAAE,kBAAkB,UAAQ;IAkG3G;;;OAGG;IACH,0BAA0B,CAAC,IAAI,EAAE,IAAI;IAyB/B,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM;IAoQnD;;;;;OAKG;IACG,mBAAmB,CAAC,UAAU,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB;IA+Y9G,OAAO,CAAC,SAAS;IAOjB;;;;OAIG;IACH,UAAU,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,EAAE,kBAAkB,CAAC,EAAE,OAAO;IA2CjE;;;;;OAKG;IACH,cAAc,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO;IAoCvE;;;OAGG;IACH,4BAA4B,CAAC,IAAI,EAAE,MAAM;IASzC;;;;OAIG;IACH,eAAe,CAAC,KAAK,EAAE,MAAM,EAAE;IA4C/B;;;OAGG;IACH,gBAAgB,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS;IAyBhD,cAAc,CAAC,OAAO,EAAE,MAAM,GAAG,SAAS,EAAE,OAAO,EAAE,MAAM,GAAG,SAAS;IAQvE,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,GAAG,OAAO;IAgBnE,wBAAwB,CAAC,IAAI,EAAE,IAAI;CAKtC;;AA4CD,wBAAiC"}