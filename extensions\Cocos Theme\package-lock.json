{"name": "cocos-theme", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "cocos-theme", "version": "1.0.0", "dependencies": {"axios": "^1.6.2", "fs-extra": "^10.0.0", "vue": "^3.1.4"}, "devDependencies": {"@types/fs-extra": "^9.0.5", "@types/node": "^16.0.1", "typescript": "^4.3.4"}}, "node_modules/@babel/parser": {"version": "7.20.1", "license": "MIT", "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@types/fs-extra": {"version": "9.0.13", "resolved": "https://registry.npmmirror.com/@types/fs-extra/-/fs-extra-9.0.13.tgz", "integrity": "sha512-nEnwB++1u5lVDM2UI4c1+5R+FYaKfaAzS4OococimjVm3nQw3TuzH5UNsocrcTBbhnerblyHj4A49qXbIiZdpA==", "dev": true, "dependencies": {"@types/node": "*"}}, "node_modules/@types/node": {"version": "16.18.66", "resolved": "https://registry.npmmirror.com/@types/node/-/node-16.18.66.tgz", "integrity": "sha512-sePmD/imfKvC4re/Wwos1NEcXYm6O96CAG5gQVY53nmDb8ePQ4qPku6uruN7n6TJ0t5FhcoUc2+yvE2/UZVDZw==", "dev": true}, "node_modules/@vue/compiler-core": {"version": "3.2.41", "license": "MIT", "dependencies": {"@babel/parser": "^7.16.4", "@vue/shared": "3.2.41", "estree-walker": "^2.0.2", "source-map": "^0.6.1"}}, "node_modules/@vue/compiler-dom": {"version": "3.2.41", "license": "MIT", "dependencies": {"@vue/compiler-core": "3.2.41", "@vue/shared": "3.2.41"}}, "node_modules/@vue/compiler-sfc": {"version": "3.2.41", "license": "MIT", "dependencies": {"@babel/parser": "^7.16.4", "@vue/compiler-core": "3.2.41", "@vue/compiler-dom": "3.2.41", "@vue/compiler-ssr": "3.2.41", "@vue/reactivity-transform": "3.2.41", "@vue/shared": "3.2.41", "estree-walker": "^2.0.2", "magic-string": "^0.25.7", "postcss": "^8.1.10", "source-map": "^0.6.1"}}, "node_modules/@vue/compiler-ssr": {"version": "3.2.41", "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.2.41", "@vue/shared": "3.2.41"}}, "node_modules/@vue/reactivity": {"version": "3.2.41", "license": "MIT", "dependencies": {"@vue/shared": "3.2.41"}}, "node_modules/@vue/reactivity-transform": {"version": "3.2.41", "license": "MIT", "dependencies": {"@babel/parser": "^7.16.4", "@vue/compiler-core": "3.2.41", "@vue/shared": "3.2.41", "estree-walker": "^2.0.2", "magic-string": "^0.25.7"}}, "node_modules/@vue/runtime-core": {"version": "3.2.41", "license": "MIT", "dependencies": {"@vue/reactivity": "3.2.41", "@vue/shared": "3.2.41"}}, "node_modules/@vue/runtime-dom": {"version": "3.2.41", "license": "MIT", "dependencies": {"@vue/runtime-core": "3.2.41", "@vue/shared": "3.2.41", "csstype": "^2.6.8"}}, "node_modules/@vue/server-renderer": {"version": "3.2.41", "license": "MIT", "dependencies": {"@vue/compiler-ssr": "3.2.41", "@vue/shared": "3.2.41"}, "peerDependencies": {"vue": "3.2.41"}}, "node_modules/@vue/shared": {"version": "3.2.41", "license": "MIT"}, "node_modules/asynckit": {"version": "0.4.0", "resolved": "https://registry.npmmirror.com/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="}, "node_modules/axios": {"version": "1.6.2", "resolved": "https://registry.npmmirror.com/axios/-/axios-1.6.2.tgz", "integrity": "sha512-7i24Ri4pmDRfJTR7LDBhsOTtcm+9kjX5WiY1X3wIisx6G9So3pfMkEiU7emUBe46oceVImccTEM3k6C5dbVW8A==", "dependencies": {"follow-redirects": "^1.15.0", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "node_modules/combined-stream": {"version": "1.0.8", "resolved": "https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/csstype": {"version": "2.6.21", "license": "MIT"}, "node_modules/delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==", "engines": {"node": ">=0.4.0"}}, "node_modules/estree-walker": {"version": "2.0.2", "license": "MIT"}, "node_modules/follow-redirects": {"version": "1.15.3", "resolved": "https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.15.3.tgz", "integrity": "sha512-1VzOtuEM8pC9SFU1E+8KfTjZyMztRsgEfwQl44z8A25uy13jSzTj6dyK2Df52iV0vgHCfBwLhDWevLn95w5v6Q==", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/form-data": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/form-data/-/form-data-4.0.0.tgz", "integrity": "sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/fs-extra": {"version": "10.1.0", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/graceful-fs": {"version": "4.2.10", "license": "ISC"}, "node_modules/jsonfile": {"version": "6.1.0", "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/magic-string": {"version": "0.25.9", "license": "MIT", "dependencies": {"sourcemap-codec": "^1.4.8"}}, "node_modules/mime-db": {"version": "1.52.0", "resolved": "https://registry.npmmirror.com/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "resolved": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/nanoid": {"version": "3.3.4", "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/picocolors": {"version": "1.0.0", "license": "ISC"}, "node_modules/postcss": {"version": "8.4.18", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.4", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/proxy-from-env": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "integrity": "sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg=="}, "node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-js": {"version": "1.0.2", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/sourcemap-codec": {"version": "1.4.8", "license": "MIT"}, "node_modules/typescript": {"version": "4.9.5", "resolved": "https://registry.npmmirror.com/typescript/-/typescript-4.9.5.tgz", "integrity": "sha512-1FXk9E2Hm+QzZQ7z+McJiHL4NW1F2EzMu9Nq9i3zAaGqibafqYwCVU6WyWAuyQRRzOlxou8xZSyXLEN8oKj24g==", "dev": true, "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=4.2.0"}}, "node_modules/universalify": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/vue": {"version": "3.2.41", "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.2.41", "@vue/compiler-sfc": "3.2.41", "@vue/runtime-dom": "3.2.41", "@vue/server-renderer": "3.2.41", "@vue/shared": "3.2.41"}}}}