{"version": 3, "file": "light-probe-tetrahedron-controller.d.ts", "sourceRoot": "", "sources": ["../../../../../../../source/script/public/gizmos/3d/elements/controller/light-probe-tetrahedron-controller.ts"], "names": [], "mappings": "AAAA,OAAO,cAAc,MAAM,mBAAmB,CAAC;AAE/C,OAAO,EACH,KAAK,EAIL,YAAY,EAGZ,IAAI,EAOP,MAAM,IAAI,CAAC;AACZ,OAA6B,EAAE,cAAc,EAAE,MAAM,0BAA0B,CAAC;AAQhF,MAAM,CAAC,OAAO,OAAO,+BAAgC,SAAQ,cAAc;IAuB5D,cAAc,EAAE,cAAc,CAAC,YAAY,CAAC;IAtBvD,MAAM,CAAC,KAAK,SAAK;IAEjB,MAAM,KAAK,IAAI,IAAI,MAAM,CAExB;IAED,MAAM,CAAC,UAAU,QAAuB;IACxC,MAAM,CAAC,SAAS,QAAuB;IAGvC,SAAS,CAAC,SAAS,UAAQ;IAG3B,OAAO,CAAC,cAAc,CAAS;IAC/B,OAAO,CAAC,YAAY,CAAqD;IACzE,OAAO,CAAC,iBAAiB,CAA0D;IACnF,OAAO,CAAC,iBAAiB,CAAc;IACvC,OAAO,CAAC,wBAAwB,CAAK;IACrC,OAAO,CAAC,YAAY,CAA0C;gBAG1D,QAAQ,EAAE,IAAI,EACP,cAAc,EAAE,cAAc,CAAC,YAAY,CAAC;IAOvD,SAAS;IAaT,OAAO,CAAC,eAAe;IA8BvB,IAAI;IAKJ,gBAAgB;IAShB,sBAAsB,CAAC,gBAAgB,EAAE,MAAM;IA6D/C,UAAU,CAAC,IAAI,EAAE,IAAI;IAKrB,aAAa;IAIb,mBAAmB,IAAI,MAAM;IAS7B,oBAAoB;CAYvB"}