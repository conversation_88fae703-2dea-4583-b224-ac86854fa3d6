export const NONE: number;
export const UP: number;
export const LEFT: number;
export const DOWN: number;
export const RIGHT: number;
export function getBlobOutlinePoints(data: any, width: any, height: any, loop: any): any[];
export function getFirstNonTransparentPixelTopDown(): {
    x: number;
    y: number;
} | null;
export function walkPerimeter(startX: any, startY: any): any[];
export function step(x: any, y: any, data: any): void;
//# sourceMappingURL=marching-squares.d.ts.map