#app {
    width: 100%;
    height: 100%;
}

.section {
    width: 100%;
}

#selection {
    width: 100%;
    height: 100%;
    overflow-y: scroll;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: wrap;
    margin-top: 10px;
}

.theme-choice {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;

    margin-bottom: 20px;

    width: 50%;
}

.color-div {
    width: 100px;
    height: 100px;
    border: rgb(131, 131, 131) 1px solid;
    border-radius: 10px;
    margin-bottom: 5px;
}

#background {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
}

.form-line {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    margin-top: 10px;
}

.btns-on-right {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    position: relative;
    top: 10px;
}