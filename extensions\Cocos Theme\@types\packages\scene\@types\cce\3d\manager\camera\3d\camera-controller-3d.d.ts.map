{"version": 3, "file": "camera-controller-3d.d.ts", "sourceRoot": "", "sources": ["../../../../../../source/script/3d/manager/camera/3d/camera-controller-3d.ts"], "names": [], "mappings": "AAAA,OAAO,EACH,gBAAgB,EAChB,mBAAmB,EACtB,MAAM,kCAAkC,CAAC;AAI1C,OAAO,oBAAoB,MAAM,2BAA2B,CAAC;AAC7D,OAAO,WAAW,MAAM,sBAAsB,CAAC;AAE/C,OAAO,EAAE,cAAc,EAAe,MAAM,UAAU,CAAC;AACvD,OAAO,EAAE,MAAM,EAAE,KAAK,EAAc,IAAI,EAAE,IAAI,EAAwB,MAAM,IAAI,CAAC;AAoBjF,aAAK,WAAW;IACZ,MAAM,WAAW;IACjB,KAAK,UAAU;IACf,OAAO,YAAY;IACnB,QAAQ,aAAa;CACxB;AACD,MAAM,WAAW,wBAAwB;IACrC,oBAAoB,EAAE,CAAC,WAAW,EAAE,MAAM,CAAC,cAAc,KAAK,IAAI,CAAC;IACnE,MAAM,EAAE,CAAC,cAAc,EAAE,cAAc,KAAK,IAAI,CAAC;IACjD,kBAAkB,EAAE,CAAC,cAAc,EAAE,cAAc,KAAK,IAAI,CAAC;CAChE;AACD,cAAM,kBAAmB,SAAQ,oBAAoB;IACjD,EAAE,CAAC,CAAC,SAAS,MAAM,wBAAwB,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,wBAAwB,CAAC,CAAC,CAAC,GAAG,IAAI;IAGnG,IAAI,CAAC,CAAC,SAAS,MAAM,wBAAwB,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,wBAAwB,CAAC,CAAC,CAAC,GAAG,IAAI;IAGrG,IAAI,CAAC,CAAC,SAAS,MAAM,wBAAwB,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,UAAU,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO;IAGzH,OAAO,CAAC,GAAG,CAAc;IACzB,OAAO,CAAC,GAAG,CAAc;IACzB,OAAO,CAAC,GAAG,CAAc;IACzB,OAAO,CAAC,GAAG,CAAc;IAEzB,2BAA2B;IAC3B,SAAS,CAAC,WAAW,SAAQ;IAC7B,SAAS,CAAC,KAAK,SAAO;IACtB,SAAS,CAAC,IAAI,SAAS;IACvB,8BAA8B;IAC9B,SAAS,CAAC,QAAQ,CAAC,WAAW,OAAO;IACrC,kBAAkB;IAClB,SAAS,CAAC,QAAQ,CAAC,UAAU,OAAO;IACpC,OAAO,CAAC,OAAO,CAAqB;IACpC,OAAO,CAAC,OAAO,CAAyE;IACxF,OAAO,CAAC,gBAAgB,CAAW;IAC5B,QAAQ,SAAM;IAGrB,OAAO,CAAC,OAAO,CAAsB;IAGrC,OAAO,CAAC,OAAO,CAAc;IAC7B,OAAO,CAAC,OAAO,CAAc;IAE7B,OAAO,CAAC,UAAU,CAAiC;IAGnD,OAAO,CAAC,QAAQ,CAAgC;IAChD,OAAO,CAAC,SAAS,CAAY;IAC7B,OAAO,CAAC,UAAU,CAAa;IAC/B,OAAO,CAAC,QAAQ,CAAW;IAC3B,OAAO,CAAC,WAAW,CAAc;IAC1B,QAAQ,IAAI,OAAO;IAG1B,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,MAAM,CAAC,EAAE,WAAW,CAAC;IACrB,MAAM,CAAC,EAAE,WAAW,CAAC;IACrB,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,MAAM,CAAC,EAAE,OAAO,CAAC;IAEjB,IAAW,SAAS,IAIQ,KAAK,CAFhC;IAED,IAAW,SAAS,CAAC,KAAK,EAAE,KAAK,EAEhC;IAED,IAAW,eAAe,IAIQ,IAAI,CAFrC;IAED,IAAW,eAAe,CAAC,KAAK,EAAE,IAAI,EAErC;IAED,IAAW,WAAW,IAIQ,MAAM,CAFnC;IAED,IAAW,WAAW,CAAC,KAAK,EAAE,MAAM,EAEnC;IAED,IAAW,kBAAkB,YAE5B;IACD,IAAW,kBAAkB,CAAC,KAAK,SAAA,EAElC;IAEM,IAAI,CAAC,MAAM,EAAE,MAAM;IAc1B,OAAO,CAAC,SAAS;IAwBjB,OAAO,CAAC,eAAe;IAMvB,IAAI,MAAM,CAAC,KAAK,EAAE,OAAO,EAaxB;IAED,UAAU,CAAC,WAAW,EAAE,WAAW;IAoBnC;;OAEG;IACI,KAAK;IAKZ;;;OAGG;IACI,sBAAsB,CAAC,QAAQ,EAAE,MAAM;IAQ9C;;;OAGG;IACI,KAAK,CAAC,KAAK,EAAE,MAAM;IA2B1B,WAAW,CAAC,KAAK,EAAE,MAAM;IAMlB,iBAAiB,EAAE,MAAM,EAAE,CAAM;IACxC;;;;;OAKG;IACI,KAAK,CAAC,SAAS,GAAE,MAAM,EAAE,GAAG,IAAW,EAAE,QAAQ,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,EAAE,IAAI,EAAE,UAAU,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,EAAE,OAAO;IAkIjH,oBAAoB,CAAC,SAAS,EAAE,MAAM,EAAE;IAoCxC,oBAAoB,CAAC,SAAS,EAAE,MAAM,EAAE;IAaxC,WAAW,CAAC,KAAK,EAAE,gBAAgB;IAoBnC,WAAW,CAAC,KAAK,EAAE,gBAAgB;IAgBnC,SAAS,CAAC,KAAK,EAAE,gBAAgB;IAejC,YAAY,CAAC,KAAK,EAAE,gBAAgB;IAQpC,SAAS,CAAC,KAAK,EAAE,mBAAmB;IAoBpC,OAAO,CAAC,KAAK,EAAE,mBAAmB;IAuBlC,QAAQ,CAAC,SAAS,EAAE,MAAM;IAI1B,eAAe,CAAC,SAAS,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,GAAE,MAAM,GAAG,IAAW;IAmFtG,UAAU;IAiBV,OAAO;IAKP,iBAAiB,CAAC,GAAG,EAAE,IAAI,EAAE,gBAAgB,EAAE,OAAO;IAwBtD,YAAY;IASnB,oBAAoB;IAgBpB,OAAO;IAMP,cAAc,CAAC,cAAc,EAAE,MAAM;IASrC,gBAAgB;IAgBT,QAAQ;CAIlB;AAED,OAAO,EAAE,kBAAkB,EAAE,CAAC"}