{"version": 3, "file": "defines.d.ts", "sourceRoot": "", "sources": ["../../../../source/script/public/gizmos/defines.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,YAAY,EAAE,SAAS,EAAE,MAAM,IAAI,CAAC;AAElF,MAAM,WAAW,cAAc;IAC3B,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;IACjC,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC;IACjB,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC;IACb,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC;IACnB,MAAM,CAAC,EAAE,IAAI,CAAC;IACd,MAAM,CAAC,EAAE,IAAI,CAAC;IACd,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,WAAW,CAAC,EAAE,OAAO,CAAC;CACzB;AAED,qBAAa,oBAAqB,YAAW,cAAc;IACvD,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC;IACnB,MAAM,CAAC,EAAE,IAAI,CAAC;IACd,MAAM,CAAC,EAAE,IAAI,CAAC;IACd,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC;IACjB,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;IACjC,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC;gBAED,SAAS,EAAE,cAAc;IAYrC,0BAA0B,IAAI,UAAU,CAAC,gBAAgB;CAS5D;AAED,MAAM,WAAW,iBAAiB;IAC9B,MAAM,CAAC,EAAE,OAAO,CAAC;CACpB;AAED,MAAM,WAAW,eAAe;IAC5B,kBAAkB;IAClB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,WAAW;IACX,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,WAAW;IACX,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,YAAY;IACZ,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,WAAW;IACX,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,qBAAqB;IACrB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,sBAAsB;IACtB,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB,sBAAsB;IACtB,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,uBAAuB;IACvB,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,0BAA0B;IAC1B,mBAAmB,CAAC,EAAE,OAAO,CAAC;IAC9B,uBAAuB;IACvB,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAChC,WAAW;IACX,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,aAAa;IACb,aAAa,CAAC,EAAE,OAAO,CAAC;CAC3B;AAED,MAAM,WAAW,oBAAqB,SAAQ,eAAe;IACzD,eAAe,CAAC,EAAE,OAAO,CAAA;IACzB,WAAW;IACX,IAAI,CAAC,EAAE,MAAM,CAAA;IACb,UAAU,CAAC,EAAE,OAAO,CAAA;CACvB;AAED,MAAM,WAAW,qBAAsB,SAAQ,oBAAoB;IAC/D,eAAe,CAAC,EAAE,OAAO,CAAC;CAC7B;AAED,MAAM,WAAW,qBAAsB,SAAQ,oBAAoB;IAC/D,UAAU,CAAC,EAAE,MAAM,CAAC;CACvB;AAED,MAAM,WAAW,0BAA0B;IACvC,UAAU,CAAC,EAAE,OAAO,CAAC;CACxB;AAED,MAAM,WAAW,kBAAkB;IAC/B,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,MAAM,EAAE,OAAO,CAAC;IAChB,OAAO,EAAE,OAAO,CAAC;IACjB,OAAO,EAAE,OAAO,CAAC;IACjB,QAAQ,EAAE,OAAO,CAAC;IAClB,UAAU,EAAE,MAAM,CAAC;IACnB,UAAU,EAAE,MAAM,CAAC;IACnB,QAAQ,EAAE,IAAI,CAAC;IACf,IAAI,EAAE,IAAI,CAAC;IACX,UAAU,EAAE,MAAM,CAAC;IACnB,UAAU,CAAC,EAAE,GAAG,CAAC;CACpB;AAED,MAAM,WAAW,WAAW;IACxB,IAAI,EAAE,MAAM,CAAA;IACZ,OAAO,EAAE,IAAI,CAAC;IACd,aAAa,EAAE,IAAI,EAAE,CAAC;IACtB,SAAS,EAAE,KAAK,EAAE,CAAC;IACnB,WAAW,EAAE,MAAM,EAAE,CAAC;IACtB,eAAe,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC;IAC9B,eAAe,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC;IAC9B,SAAS,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC;IACxB,aAAa,CAAC,EAAE,YAAY,CAAC;IAC7B,QAAQ,CAAC,EAAE,IAAI,CAAC;IAChB,UAAU,CAAC,EAAE,GAAG,CAAC;CACpB"}