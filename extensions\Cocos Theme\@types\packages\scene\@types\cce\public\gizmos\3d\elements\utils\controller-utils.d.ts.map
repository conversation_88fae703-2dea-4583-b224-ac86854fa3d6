{"version": 3, "file": "controller-utils.d.ts", "sourceRoot": "", "sources": ["../../../../../../../source/script/public/gizmos/3d/elements/utils/controller-utils.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAgB,IAAI,EAAE,IAAI,EAAM,IAAI,EAAE,MAAM,IAAI,CAAC;AAChF,OAAO,EAAwB,cAAc,EAAE,oBAAoB,EAAE,qBAAqB,EAAE,qBAAqB,EAAE,MAAM,kBAAkB,CAAC;AAmB5I,aAAK,QAAQ;IACT,CAAC,MAAM;IACP,CAAC,MAAM;IACP,CAAC,MAAM;IACP,KAAK,UAAU;IACf,KAAK,UAAU;IACf,KAAK,UAAU;CAClB;AAED,cAAM,eAAe;IACjB,OAAc,QAAQ,kBAAY;IAClC,OAAc,gBAAgB,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI,CAAA;KAAE,CAOrD;WAEY,KAAK,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,GAAE,OAAO,CAAC,qBAAqB,CAAM;WA+CzH,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAE,QAAQ,CAAC,IAAI,CAAqB,EAAE,KAAK,GAAE,KAAiB,EAAE,IAAI,GAAE,OAAO,CAAC,qBAAqB,CAAM;WAO3K,WAAW,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM;WA4BxE,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK;WAQ/D,KAAK,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK;WAY3D,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,KAAK,EAAE,IAAI,GAAE,OAAO,CAAC,oBAAoB,CAAM;WAUzH,WAAW,CAAC,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,GAAE,OAAO,CAAC,oBAAoB,CAAM;WAoBzG,uBAAuB,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI;WAO/C,MAAM,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,GAAE,KAAiB,EAAE,IAAI,GAAE,OAAO,CAAC,oBAAoB,CAAM;WASvG,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,GAAE,KAAiB,EAAE,IAAI,GAAE,OAAO,CAAC,oBAAoB,CAAM;WASvI,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,GAAE,KAAiB,EAAE,IAAI,GAAE,OAAO,CAAC,oBAAoB,CAAM;WASpJ,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,GAAE,KAAiB,EAAE,IAAI,GAAE,OAAO,CAAC,oBAAoB,CAAM;WAQjJ,gBAAgB,CAC1B,MAAM,EAAE,IAAI,EACZ,MAAM,EAAE,IAAI,EACZ,OAAO,EAAE,IAAI,EACb,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,MAAM,EACd,QAAQ,EAAE,MAAM,EAChB,KAAK,GAAE,KAAiB;WASd,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,KAAK,GAAE,KAAiB,EAAE,IAAI,GAAE,OAAO,CAAC,oBAAoB,CAAM;WAQ7G,YAAY,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,GAAE,OAAO,CAAC,oBAAoB,CAAM;WAQ7F,OAAO,CACjB,OAAO,EAAE,OAAO,EAChB,WAAW,EAAE,MAAM,EACnB,GAAG,EAAE,MAAM,EACX,MAAM,EAAE,MAAM,EACd,IAAI,EAAE,MAAM,EACZ,GAAG,EAAE,MAAM,EACX,KAAK,EAAE,KAAK,EACZ,IAAI,GAAE,OAAO,CAAC,oBAAoB,CAAM;WAS9B,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,GAAE,OAAO,CAAC,oBAAoB,CAAM;WAQnH,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI;WAU1B,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,GAAE,OAAO,CAAC,oBAAoB,CAAM,EAAE,aAAa,CAAC,EAAE,QAAQ;WAOrH,UAAU,CAAC,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,oBAAM,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,GAAE,OAAO,CAAC,oBAAoB,CAAM;WAQjJ,iBAAiB,CAAC,SAAS,EAAE,cAAc,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,GAAE,OAAO,CAAC,oBAAoB,CAAM,EAAE,aAAa,CAAC,EAAE,QAAQ;WAQ7H,YAAY,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI;WAIjC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,KAAK,GAAE,KAAiB;WAmBnF,eAAe,CAAC,SAAS,EAAE,IAAI,EAAE;WAQjC,eAAe,CAAC,SAAS,EAAE,IAAI,EAAE;CAOlD;AAED,eAAe,eAAe,CAAC"}