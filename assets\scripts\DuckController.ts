import { _decorator, Component, Node, Vec3, randomRangeInt, Vec2 } from 'cc';
const { ccclass, property } = _decorator;

export enum DuckState {
    FREE,      // 自由状态
    SCARED,    // 惊吓状态
    CLUSTERED  // 集群状态
}

@ccclass('DuckController')
export class Duck<PERSON>ontroller extends Component {
    @property
    public duckColor: string = 'red';
    
    @property
    public freeSpeed: number = 100;
    
    @property
    public scaredSpeed: number = 200;
    
    @property
    public clusterSpeed: number = 80;
    
    private currentState: DuckState = DuckState.FREE;
    private moveDirection: Vec2 = new Vec2();
    private stateTimer: number = 0;
    private scaredDuration: number = 3;

    start() {
        this.changeDirection();
        this.schedule(this.updateMovement, 0.016); // 60fps
    }

    updateMovement() {
        const deltaTime = 0.016;
        
        switch (this.currentState) {
            case DuckState.FREE:
                this.updateFreeMovement(deltaTime);
                break;
            case DuckState.SCARED:
                this.updateScaredMovement(deltaTime);
                break;
            case DuckState.CLUSTERED:
                this.updateClusteredMovement(deltaTime);
                break;
        }
        
        // 边界检测
        this.checkBoundaries();
    }

    private updateFreeMovement(deltaTime: number) {
        // 自由状态移动逻辑
        const movement = new Vec3(
            this.moveDirection.x * this.freeSpeed * deltaTime,
            this.moveDirection.y * this.freeSpeed * deltaTime,
            0
        );
        this.node.position = this.node.position.add(movement);
        
        // 随机改变方向
        this.stateTimer += deltaTime;
        if (this.stateTimer > randomRangeInt(2, 4)) {
            this.changeDirection();
            this.stateTimer = 0;
        }
    }

    private updateScaredMovement(deltaTime: number) {
        // 惊吓状态移动逻辑
        const movement = new Vec3(
            this.moveDirection.x * this.scaredSpeed * deltaTime,
            this.moveDirection.y * this.scaredSpeed * deltaTime,
            0
        );
        this.node.position = this.node.position.add(movement);
        
        // 检查是否结束惊吓状态
        this.stateTimer += deltaTime;
        if (this.stateTimer >= this.scaredDuration) {
            this.setState(DuckState.FREE);
        }
    }

    private updateClusteredMovement(deltaTime: number) {
        // 集群状态移动逻辑（较慢的移动）
        const movement = new Vec3(
            this.moveDirection.x * this.clusterSpeed * deltaTime,
            this.moveDirection.y * this.clusterSpeed * deltaTime,
            0
        );
        this.node.position = this.node.position.add(movement);
        
        // 集群状态下也会偶尔改变方向
        this.stateTimer += deltaTime;
        if (this.stateTimer > randomRangeInt(4, 8)) {
            this.changeDirection();
            this.stateTimer = 0;
        }
    }

    private changeDirection() {
        const angle = Math.random() * Math.PI * 2;
        this.moveDirection.set(Math.cos(angle), Math.sin(angle));
    }

    private checkBoundaries() {
        const pos = this.node.position;
        const margin = 50; // 边界缓冲区
        
        // 检查是否接近边界，如果是则改变方向
        if (pos.x > 325 || pos.x < -325 || pos.y > 617 || pos.y < -617) {
            // 计算远离边界的方向
            const centerDirection = new Vec2(-pos.x, -pos.y).normalize();
            this.moveDirection = centerDirection;
            
            // 如果撞到边界且不是惊吓状态，则进入短暂惊吓
            if (this.currentState === DuckState.FREE) {
                this.setState(DuckState.SCARED);
            }
        }
    }

    public setState(newState: DuckState) {
        this.currentState = newState;
        this.stateTimer = 0;
        
        // 状态切换时的特殊处理
        if (newState === DuckState.SCARED) {
            // 惊吓状态下随机选择逃跑方向
            this.changeDirection();
        }
    }

    public getState(): DuckState {
        return this.currentState;
    }

    public setScaredDirection(dogPosition: Vec3) {
        // 设置远离猎犬的方向
        const escapeDirection = this.node.position.subtract(dogPosition).normalize();
        this.moveDirection.set(escapeDirection.x, escapeDirection.y);
        this.setState(DuckState.SCARED);
    }
}