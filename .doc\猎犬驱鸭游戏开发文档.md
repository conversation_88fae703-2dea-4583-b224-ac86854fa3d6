# 《猎犬驱鸭》微信小程序游戏开发文档

## 1. 项目概述

### 1.1 游戏简介
《猎犬驱鸭》是一款基于微信小程序的休闲益智2D游戏。玩家通过触屏操作控制猎犬移动，将场景中所有同色鸭子驱赶成集群状态。游戏支持微信排行榜系统，记录玩家通关时间并进行排名。

### 1.2 技术栈选择
- **游戏引擎**: Cocos Creator 3.8.0
- **开发平台**: 微信小游戏
- **编程语言**: TypeScript
- **排行榜**: 微信开放数据域
- **云服务**: 微信云开发（可选）

### 1.3 开发环境要求
- 操作系统：Windows 10/11 或 macOS 10.14+
- 内存：8GB以上
- 硬盘空间：5GB以上
- 网络：稳定的互联网连接

## 2. 技术方案设计

### 2.1 Cocos Creator 3.8.0 版本特性
- 稳定的渲染管线
- 完善的微信小游戏支持
- 优化的性能表现
- 丰富的组件系统

### 2.2 微信小游戏集成方案
```typescript
// 微信API调用示例
wx.onShow(() => {
    // 游戏显示时的处理
});

wx.getSystemInfo({
    success: (res) => {
        // 获取设备信息
    }
});
```

### 2.3 排行榜系统设计
- 使用微信开放数据域实现
- 记录玩家最佳通关时间
- 支持好友排行榜
- 周榜/月榜功能

### 2.4 游戏架构设计
```
GameManager (游戏管理器)
├── SceneManager (场景管理)
├── DogController (猎犬控制器)
├── DuckManager (鸭子管理器)
├── UIManager (界面管理器)
└── RankManager (排行榜管理器)
```

## 3. 游戏设计文档

### 3.1 核心玩法机制
1. **触屏控制**: 玩家点击屏幕，猎犬移动到点击位置
2. **驱赶机制**: 猎犬接近鸭子时，鸭子进入惊吓状态
3. **集群判定**: 同色鸭子距离小于设定值时形成集群
4. **胜利条件**: 所有颜色的鸭子都形成集群

### 3.2 游戏对象行为规则

#### 猎犬行为
- **移动方式**: 线性插值移动到目标位置
- **移动速度**: 300像素/秒（可调节）
- **检测范围**: 半径80像素

#### 鸭子状态机
```typescript
enum DuckState {
    FREE,      // 自由状态
    SCARED,    // 惊吓状态
    CLUSTERED  // 集群状态
}
```

**自由状态**:
- 随机方向移动，速度100像素/秒
- 每2-4秒随机改变方向

**惊吓状态**:
- 快速远离猎犬，速度200像素/秒
- 持续时间3秒后回到自由状态

**集群状态**:
- 同色鸭子作为整体移动
- 移动速度80像素/秒
- 集群半径60像素

### 3.3 关卡设计与难度曲线

| 关卡 | 颜色数量 | 每色鸭子数 | 基础速度 | 特殊机制 |
| ---- | -------- | ---------- | -------- | -------- |
| 1    | 2        | 3-4        | 100      | 无       |
| 2    | 2        | 4-5        | 110      | 无       |
| 3    | 3        | 3-4        | 120      | 无       |
| 4    | 3        | 4-5        | 130      | 无       |
| 5    | 4        | 3-4        | 140      | 集群解散 |
| 6+   | 递增     | 3-5        | +10/关   | 集群解散 |

### 3.4 游戏平衡性参数
```typescript
// 推荐的游戏参数配置
const GameConfig = {
    dog: {
        speed: 300,           // 猎犬移动速度
        detectionRadius: 80   // 检测半径
    },
    duck: {
        freeSpeed: 100,       // 自由状态速度
        scaredSpeed: 200,     // 惊吓状态速度
        clusterSpeed: 80,     // 集群状态速度
        scaredDuration: 3,    // 惊吓持续时间(秒)
        clusterRadius: 60,    // 集群半径
        clusterDistance: 50   // 集群判定距离
    },
    level: {
        clusterTimeout: 30,   // 集群解散时间(秒)
        speedIncrement: 10    // 每关速度增量
    }
};
```

## 4. 开发环境搭建教程

### 4.1 Cocos Creator 3.8.0 安装配置

#### 步骤1：下载安装包
1. 访问 [Cocos Creator官网](https://www.cocos.com/creator)
2. 点击"下载"按钮
3. 选择"Cocos Creator 3.8.0"版本
4. 根据操作系统选择对应安装包

#### 步骤2：安装过程
1. 双击下载的安装包
2. 选择安装路径（建议默认路径）
3. 等待安装完成（约5-10分钟）
4. 首次启动会要求登录Cocos账号

#### 步骤3：配置开发环境
1. 打开Cocos Creator
2. 点击"偏好设置"
3. 在"引擎管理"中确认3.8.0版本已安装
4. 在"外部程序"中配置代码编辑器（推荐VS Code）

### 4.2 微信开发者工具配置

#### 步骤1：下载安装
1. 访问[微信开发者工具官网](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
2. 下载最新稳定版
3. 安装并启动

#### 步骤2：配置小游戏项目
1. 使用微信扫码登录
2. 选择"小游戏"项目类型
3. 填写项目信息：
   - 项目名称：猎犬驱鸭
   - 目录：选择空文件夹
   - AppID：测试号或正式AppID

### 4.3 项目初始化步骤

#### 步骤1：创建Cocos Creator项目
1. 打开Cocos Creator
2. 点击"新建项目"
3. 选择"2D项目"模板
4. 项目名称：DogHerdingGame
5. 项目路径：选择合适位置
6. 点击"创建并打开"

#### 步骤2：配置微信小游戏构建
1. 在Cocos Creator中点击"项目" → "构建发布"
2. 选择平台："微信小游戏"
3. 配置参数：
   - 游戏名称：猎犬驱鸭
   - 游戏图标：上传512x512像素图标
   - 游戏版本：1.0.0
4. 点击"构建"

## 5. 详细开发步骤

### 5.1 项目创建与基础配置

#### 创建基础场景结构
1. 在"资源管理器"中创建文件夹结构：
```
assets/
├── scenes/          # 场景文件
├── scripts/         # 脚本文件
├── textures/        # 贴图资源
├── prefabs/         # 预制体
└── audio/           # 音频文件
```

2. 创建主游戏场景：
   - 右键"scenes"文件夹 → "创建" → "场景"
   - 命名为"GameScene"
   - 双击打开场景

#### 配置画布和摄像机
1. 选中"Canvas"节点
2. 在"属性检查器"中设置：
   - 设计分辨率：750 x 1334
   - 适配模式：SHOW_ALL
3. 选中"Main Camera"节点
4. 设置摄像机参数：
   - 投影模式：正交投影
   - 正交高度：667

### 5.2 场景搭建教程

#### 创建游戏背景
1. 在"层级管理器"中右键 → "创建" → "2D对象" → "Sprite"
2. 命名为"Background"
3. 在"属性检查器"中：
   - 设置位置：(0, 0, 0)
   - 设置尺寸：(750, 1334)
   - 拖入背景贴图到"SpriteFrame"属性

#### 创建游戏边界
1. 创建空节点命名为"Boundaries"
2. 添加四个子节点作为边界墙：
   - TopWall: 位置(0, 667, 0)，尺寸(750, 20)
   - BottomWall: 位置(0, -667, 0)，尺寸(750, 20)
   - LeftWall: 位置(-375, 0, 0)，尺寸(20, 1334)
   - RightWall: 位置(375, 0, 0)，尺寸(20, 1334)

### 5.3 游戏对象实现

#### 创建猎犬预制体
1. 在"层级管理器"中右键 → "创建" → "2D对象" → "Sprite"
2. 命名为"Dog"
3. 添加组件：
   - RigidBody2D（2D刚体组件）
   - BoxCollider2D（2D碰撞器组件）
4. 创建脚本"DogController.ts"：

```typescript
import { _decorator, Component, Node, Vec3, input, Input, EventTouch, tween, Camera } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('DogController')
export class DogController extends Component {
    @property
    public speed: number = 300;
    
    @property(Camera)
    public camera: Camera = null;
    
    private targetPosition: Vec3 = new Vec3();
    private isMoving: boolean = false;

    start() {
        input.on(Input.EventType.TOUCH_START, this.onTouchStart, this);
        
        // 如果没有指定摄像机，自动获取主摄像机
        if (!this.camera) {
            this.camera = Camera.main;
        }
    }

    onTouchStart(event: EventTouch) {
        const touchPos = event.getUILocation();
        
        // 转换屏幕坐标到世界坐标（2D）
        this.camera.screenToWorld(new Vec3(touchPos.x, touchPos.y, 0), this.targetPosition);
        this.moveToTarget();
    }

    moveToTarget() {
        if (this.isMoving) return;
        
        this.isMoving = true;
        const distance = Vec3.distance(this.node.position, this.targetPosition);
        const duration = distance / this.speed;

        tween(this.node)
            .to(duration, { position: this.targetPosition })
            .call(() => {
                this.isMoving = false;
            })
            .start();
    }
}
```

#### 创建鸭子预制体
1. 在"层级管理器"中右键 → "创建" → "2D对象" → "Sprite"
2. 命名为"Duck"
3. 添加组件：
   - RigidBody2D（2D刚体组件）
   - CircleCollider2D（2D圆形碰撞器）
4. 创建脚本"DuckController.ts"：

```typescript
import { _decorator, Component, Node, Vec3, randomRangeInt, Vec2 } from 'cc';
const { ccclass, property } = _decorator;

export enum DuckState {
    FREE,      // 自由状态
    SCARED,    // 惊吓状态
    CLUSTERED  // 集群状态
}

@ccclass('DuckController')
export class DuckController extends Component {
    @property
    public duckColor: string = 'red';
    
    @property
    public freeSpeed: number = 100;
    
    @property
    public scaredSpeed: number = 200;
    
    @property
    public clusterSpeed: number = 80;
    
    private currentState: DuckState = DuckState.FREE;
    private moveDirection: Vec2 = new Vec2();
    private stateTimer: number = 0;
    private scaredDuration: number = 3;

    start() {
        this.changeDirection();
        this.schedule(this.updateMovement, 0.016); // 60fps
    }

    updateMovement() {
        const deltaTime = 0.016;
        
        switch (this.currentState) {
            case DuckState.FREE:
                this.updateFreeMovement(deltaTime);
                break;
            case DuckState.SCARED:
                this.updateScaredMovement(deltaTime);
                break;
            case DuckState.CLUSTERED:
                this.updateClusteredMovement(deltaTime);
                break;
        }
        
        // 边界检测
        this.checkBoundaries();
    }

    private updateFreeMovement(deltaTime: number) {
        // 自由状态移动逻辑
        const movement = new Vec3(
            this.moveDirection.x * this.freeSpeed * deltaTime,
            this.moveDirection.y * this.freeSpeed * deltaTime,
            0
        );
        this.node.position = this.node.position.add(movement);
        
        // 随机改变方向
        this.stateTimer += deltaTime;
        if (this.stateTimer > randomRangeInt(2, 4)) {
            this.changeDirection();
            this.stateTimer = 0;
        }
    }

    private updateScaredMovement(deltaTime: number) {
        // 惊吓状态移动逻辑
        const movement = new Vec3(
            this.moveDirection.x * this.scaredSpeed * deltaTime,
            this.moveDirection.y * this.scaredSpeed * deltaTime,
            0
        );
        this.node.position = this.node.position.add(movement);
        
        // 检查是否结束惊吓状态
        this.stateTimer += deltaTime;
        if (this.stateTimer >= this.scaredDuration) {
            this.setState(DuckState.FREE);
        }
    }

    private updateClusteredMovement(deltaTime: number) {
        // 集群状态移动逻辑（较慢的移动）
        const movement = new Vec3(
            this.moveDirection.x * this.clusterSpeed * deltaTime,
            this.moveDirection.y * this.clusterSpeed * deltaTime,
            0
        );
        this.node.position = this.node.position.add(movement);
        
        // 集群状态下也会偶尔改变方向
        this.stateTimer += deltaTime;
        if (this.stateTimer > randomRangeInt(4, 8)) {
            this.changeDirection();
            this.stateTimer = 0;
        }
    }

    private changeDirection() {
        const angle = Math.random() * Math.PI * 2;
        this.moveDirection.set(Math.cos(angle), Math.sin(angle));
    }

    private checkBoundaries() {
        const pos = this.node.position;
        const margin = 50; // 边界缓冲区
        
        // 检查是否接近边界，如果是则改变方向
        if (pos.x > 325 || pos.x < -325 || pos.y > 617 || pos.y < -617) {
            // 计算远离边界的方向
            const centerDirection = new Vec2(-pos.x, -pos.y).normalize();
            this.moveDirection = centerDirection;
            
            // 如果撞到边界且不是惊吓状态，则进入短暂惊吓
            if (this.currentState === DuckState.FREE) {
                this.setState(DuckState.SCARED);
            }
        }
    }

    public setState(newState: DuckState) {
        this.currentState = newState;
        this.stateTimer = 0;
        
        // 状态切换时的特殊处理
        if (newState === DuckState.SCARED) {
            // 惊吓状态下随机选择逃跑方向
            this.changeDirection();
        }
    }

    public getState(): DuckState {
        return this.currentState;
    }

    public setScaredDirection(dogPosition: Vec3) {
        // 设置远离猎犬的方向
        const escapeDirection = this.node.position.subtract(dogPosition).normalize();
        this.moveDirection.set(escapeDirection.x, escapeDirection.y);
        this.setState(DuckState.SCARED);
    }
}
```

### 5.4 状态机系统开发

#### 游戏管理器实现
创建"GameManager.ts"脚本：

```typescript
import { _decorator, Component, Node, Prefab, instantiate, Vec3 } from 'cc';
import { DuckController, DuckState } from './DuckController';
import { DogController } from './DogController';

const { ccclass, property } = _decorator;

@ccclass('GameManager')
export class GameManager extends Component {
    @property(Prefab)
    public duckPrefab: Prefab = null;
    
    @property(Prefab)
    public dogPrefab: Prefab = null;
    
    @property
    public currentLevel: number = 1;
    
    private ducks: DuckController[] = [];
    private dog: DogController = null;
    private gameStartTime: number = 0;

    start() {
        this.initializeLevel();
        this.gameStartTime = Date.now();
    }

    initializeLevel() {
        this.createDog();
        this.createDucks();
    }

    createDog() {
        const dogNode = instantiate(this.dogPrefab);
        dogNode.setPosition(0, -500, 0);
        this.node.addChild(dogNode);
        this.dog = dogNode.getComponent(DogController);
    }

    createDucks() {
        const levelConfig = this.getLevelConfig(this.currentLevel);
        
        for (let colorIndex = 0; colorIndex < levelConfig.colors; colorIndex++) {
            const color = this.getColorName(colorIndex);
            
            for (let i = 0; i < levelConfig.ducksPerColor; i++) {
                const duckNode = instantiate(this.duckPrefab);
                const randomPos = this.getRandomPosition();
                duckNode.setPosition(randomPos);
                
                const duckController = duckNode.getComponent(DuckController);
                duckController.duckColor = color;
                
                this.node.addChild(duckNode);
                this.ducks.push(duckController);
            }
        }
    }

    private getLevelConfig(level: number) {
        if (level <= 2) return { colors: 2, ducksPerColor: 3 + level };
        if (level <= 4) return { colors: 3, ducksPerColor: 3 + (level - 2) };
        return { colors: Math.min(4 + Math.floor((level - 5) / 2), 6), ducksPerColor: 4 };
    }

    private getColorName(index: number): string {
        const colors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange'];
        return colors[index];
    }

    private getRandomPosition(): Vec3 {
        const x = (Math.random() - 0.5) * 600;
        const y = (Math.random() - 0.5) * 800;
        return new Vec3(x, y, 0);
    }

    update() {
        this.checkCollisions();
        this.checkClusters();
        this.checkWinCondition();
    }

    private checkCollisions() {
        const dogPos = this.dog.node.position;
        
        this.ducks.forEach(duck => {
            const distance = Vec3.distance(dogPos, duck.node.position);
            if (distance < 80 && duck.getState() !== DuckState.SCARED) {
                duck.setState(DuckState.SCARED);
            }
        });
    }

    private checkClusters() {
        const colorGroups = this.groupDucksByColor();
        
        Object.keys(colorGroups).forEach(color => {
            const ducksOfColor = colorGroups[color];
            this.updateClusterState(ducksOfColor);
        });
    }

    private groupDucksByColor() {
        const groups = {};
        this.ducks.forEach(duck => {
            if (!groups[duck.duckColor]) {
                groups[duck.duckColor] = [];
            }
            groups[duck.duckColor].push(duck);
        });
        return groups;
    }

    private updateClusterState(ducks: DuckController[]) {
        // 检查是否形成集群的逻辑
        let clusteredCount = 0;
        
        for (let i = 0; i < ducks.length; i++) {
            let nearbyCount = 0;
            for (let j = 0; j < ducks.length; j++) {
                if (i !== j) {
                    const distance = Vec3.distance(ducks[i].node.position, ducks[j].node.position);
                    if (distance < 50) {
                        nearbyCount++;
                    }
                }
            }
            
            if (nearbyCount >= ducks.length - 1) {
                ducks[i].setState(DuckState.CLUSTERED);
                clusteredCount++;
            }
        }
    }

    private checkWinCondition() {
        const colorGroups = this.groupDucksByColor();
        let allClustered = true;
        
        Object.keys(colorGroups).forEach(color => {
            const ducksOfColor = colorGroups[color];
            const clusteredDucks = ducksOfColor.filter(duck => duck.getState() === DuckState.CLUSTERED);
            
            if (clusteredDucks.length !== ducksOfColor.length) {
                allClustered = false;
            }
        });
        
        if (allClustered) {
            this.onLevelComplete();
        }
    }

    private onLevelComplete() {
        const completionTime = Date.now() - this.gameStartTime;
        console.log(`关卡完成！用时：${completionTime}ms`);
        
        // 上传分数到微信排行榜
        this.uploadScore(completionTime);
        
        // 进入下一关
        this.currentLevel++;
        this.initializeLevel();
    }

    private uploadScore(time: number) {
        // 微信排行榜上传逻辑
        if (typeof wx !== 'undefined') {
            wx.setUserCloudStorage({
                KVDataList: [{
                    key: `level_${this.currentLevel}_time`,
                    value: time.toString()
                }],
                success: () => {
                    console.log('分数上传成功');
                }
            });
        }
    }
}
```

### 5.5 UI界面制作

#### 创建游戏UI
1. 在Canvas下创建UI节点结构：
```
Canvas
├── GameUI
│   ├── TopPanel
│   │   ├── LevelLabel
│   │   ├── TimeLabel
│   │   └── ScoreLabel
│   ├── PauseButton
│   └── SettingsButton
└── MenuUI
    ├── StartButton
    ├── RankButton
    └── SettingsButton
```

#### UI管理器脚本
创建"UIManager.ts"：

```typescript
import { _decorator, Component, Node, Label, Button } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('UIManager')
export class UIManager extends Component {
    @property(Label)
    public levelLabel: Label = null;
    
    @property(Label)
    public timeLabel: Label = null;
    
    @property(Button)
    public pauseButton: Button = null;

    private gameStartTime: number = 0;
    private currentLevel: number = 1;

    start() {
        this.gameStartTime = Date.now();
        this.updateLevelDisplay();
        
        // 绑定按钮事件
        this.pauseButton.node.on(Button.EventType.CLICK, this.onPauseClick, this);
    }

    update() {
        this.updateTimeDisplay();
    }

    updateLevelDisplay() {
        if (this.levelLabel) {
            this.levelLabel.string = `第 ${this.currentLevel} 关`;
        }
    }

    updateTimeDisplay() {
        if (this.timeLabel) {
            const elapsed = Math.floor((Date.now() - this.gameStartTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            this.timeLabel.string = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }

    onPauseClick() {
        // 暂停游戏逻辑
        console.log('游戏暂停');
    }

    setLevel(level: number) {
        this.currentLevel = level;
        this.updateLevelDisplay();
    }

    resetTimer() {
        this.gameStartTime = Date.now();
    }
}
```

### 5.6 微信API集成

#### 微信小游戏适配
创建"WeChatManager.ts"：

```typescript
import { _decorator, Component, sys } from 'cc';
const { ccclass } = _decorator;

@ccclass('WeChatManager')
export class WeChatManager extends Component {
    private static instance: WeChatManager = null;

    public static getInstance(): WeChatManager {
        return WeChatManager.instance;
    }

    onLoad() {
        WeChatManager.instance = this;
        this.initWeChat();
    }

    private initWeChat() {
        if (sys.platform === sys.Platform.WECHAT_GAME) {
            // 监听微信小游戏生命周期
            wx.onShow(() => {
                console.log('游戏显示');
                // 恢复游戏音效等
            });

            wx.onHide(() => {
                console.log('游戏隐藏');
                // 暂停游戏，保存进度
            });

            // 获取系统信息
            wx.getSystemInfo({
                success: (res) => {
                    console.log('设备信息：', res);
                }
            });
        }
    }

    // 分享功能
    public shareGame(title: string = '快来挑战猎犬驱鸭！') {
        if (typeof wx !== 'undefined') {
            wx.shareAppMessage({
                title: title,
                imageUrl: 'images/share.jpg' // 分享图片
            });
        }
    }

    // 显示排行榜
    public showRankList() {
        if (typeof wx !== 'undefined') {
            wx.showOpenData({
                templateId: 'rankList',
                style: {
                    left: 0,
                    top: 0,
                    width: 750,
                    height: 1334
                }
            });
        }
    }

    // 上传分数
    public uploadScore(score: number, level: number) {
        if (typeof wx !== 'undefined') {
            wx.setUserCloudStorage({
                KVDataList: [{
                    key: 'best_time',
                    value: JSON.stringify({
                        score: score,
                        level: level,
                        timestamp: Date.now()
                    })
                }],
                success: () => {
                    console.log('分数上传成功');
                },
                fail: (err) => {
                    console.error('分数上传失败：', err);
                }
            });
        }
    }

    // 获取好友排行榜数据
    public getFriendRankData() {
        if (typeof wx !== 'undefined') {
            wx.getFriendCloudStorage({
                keyList: ['best_time'],
                success: (res) => {
                    console.log('好友排行榜数据：', res.data);
                }
            });
        }
    }
}
```

## 6. 微信小程序发布指南

### 6.1 小程序注册配置

#### 注册小程序账号
1. 访问[微信公众平台](https://mp.weixin.qq.com/)
2. 点击"立即注册" → 选择"小程序"
3. 填写账号信息并完成邮箱验证
4. 完成身份认证（个人或企业）
5. 获取AppID

#### 配置小程序信息
1. 登录小程序后台
2. 在"设置" → "基本设置"中：
   - 填写小程序名称：猎犬驱鸭
   - 上传小程序头像（144x144像素）
   - 填写小程序介绍
   - 选择服务类别：游戏 → 休闲游戏

### 6.2 发布前检查清单

#### 代码检查
- [ ] 所有功能正常运行
- [ ] 没有console.log等调试代码
- [ ] 资源文件大小控制在4MB以内
- [ ] 适配不同屏幕尺寸

#### 资源检查
- [ ] 游戏图标（512x512像素）
- [ ] 分享图片（5:4比例，建议500x400像素）
- [ ] 启动页面截图
- [ ] 功能页面截图（至少3张）

#### 合规检查
- [ ] 游戏内容健康向上
- [ ] 没有违规内容
- [ ] 用户隐私保护
- [ ] 适龄提示

### 6.3 构建发布流程

#### 步骤1：Cocos Creator构建
1. 在Cocos Creator中点击"项目" → "构建发布"
2. 选择"微信小游戏"平台
3. 配置构建参数：
   - 发布路径：选择空文件夹
   - 游戏名称：猎犬驱鸭
   - AppID：填入小程序AppID
   - 资源服务器地址：（可选）
4. 点击"构建"并等待完成

#### 步骤2：微信开发者工具调试
1. 打开微信开发者工具
2. 导入构建后的项目
3. 进行功能测试：
   - 游戏基本功能
   - 微信API调用
   - 性能表现
   - 不同设备适配

#### 步骤3：上传代码
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 上传成功后在小程序后台查看

#### 步骤4：提交审核
1. 登录小程序后台
2. 在"版本管理"中找到上传的版本
3. 点击"提交审核"
4. 填写审核信息：
   - 功能页面：选择游戏主要页面
   - 功能描述：详细描述游戏玩法
   - 测试账号：（如需要）

### 6.4 审核要点与注意事项

#### 常见审核问题
1. **功能描述不清晰**
   - 解决：详细描述游戏玩法和特色
   
2. **缺少必要页面**
   - 解决：确保有完整的游戏流程

3. **性能问题**
   - 解决：优化资源大小，提升加载速度

4. **适配问题**
   - 解决：测试多种设备尺寸

#### 审核通过后
1. 设置体验版本供内测
2. 收集用户反馈
3. 修复发现的问题
4. 正式发布上线

## 7. 开发时间规划

### 7.1 各阶段时间估算

| 阶段  | 任务内容             | 预计时间 | 备注               |
| ----- | -------------------- | -------- | ------------------ |
| 第1周 | 环境搭建、项目初始化 | 2-3天    | 包含学习时间       |
| 第1周 | 基础场景搭建         | 2-3天    | 背景、边界等       |
| 第2周 | 猎犬控制实现         | 3-4天    | 触屏控制、移动     |
| 第2周 | 鸭子基础行为         | 2-3天    | 自由状态移动       |
| 第3周 | 状态机系统           | 4-5天    | 惊吓、集群状态     |
| 第3周 | 碰撞检测             | 1-2天    | 猎犬与鸭子         |
| 第4周 | UI界面制作           | 3-4天    | 游戏界面、菜单     |
| 第4周 | 关卡系统             | 2-3天    | 难度递增           |
| 第5周 | 微信API集成          | 3-4天    | 排行榜、分享       |
| 第5周 | 音效音乐             | 1-2天    | 背景音乐、音效     |
| 第6周 | 测试优化             | 3-4天    | Bug修复、性能优化  |
| 第6周 | 发布准备             | 2-3天    | 资源整理、提交审核 |

### 7.2 里程碑节点

#### 里程碑1（第1周末）：基础框架完成
- 开发环境搭建完毕
- 项目基础结构建立
- 场景基本搭建完成

#### 里程碑2（第2周末）：核心玩法实现
- 猎犬控制功能完成
- 鸭子基础移动实现
- 基本交互可以进行

#### 里程碑3（第3周末）：游戏逻辑完善
- 完整状态机系统
- 集群判定逻辑
- 胜利条件检测

#### 里程碑4（第4周末）：功能完整版本
- UI界面完成
- 关卡系统实现
- 基本游戏流程完整

#### 里程碑5（第5周末）：微信集成版本
- 微信API集成完成
- 排行榜功能实现
- 分享功能正常

#### 里程碑6（第6周末）：发布就绪版本
- 所有功能测试通过
- 性能优化完成
- 提交审核

### 7.3 风险评估

#### 高风险项
1. **微信API集成复杂度**
   - 风险：API文档理解困难
   - 应对：提前学习，寻求技术支持

2. **游戏平衡性调试**
   - 风险：参数调试耗时较长
   - 应对：预留充足测试时间

#### 中风险项
1. **状态机系统复杂度**
   - 风险：逻辑实现困难
   - 应对：分步实现，逐步完善

2. **性能优化**
   - 风险：低端设备运行卡顿
   - 应对：及早进行性能测试

#### 低风险项
1. **UI界面制作**
   - 风险：设计不够美观
   - 应对：参考优秀案例

2. **音效音乐**
   - 风险：资源获取困难
   - 应对：使用免费音效库

## 8. 调试与优化

### 8.1 游戏平衡性调试参数

#### 核心参数配置表
```typescript
// 建议的参数调试范围
const BalanceConfig = {
    dog: {
        speed: {
            min: 250,
            default: 300,
            max: 400,
            description: "猎犬移动速度，影响操作手感"
        },
        detectionRadius: {
            min: 60,
            default: 80,
            max: 120,
            description: "检测半径，影响游戏难度"
        }
    },
    duck: {
        freeSpeed: {
            min: 80,
            default: 100,
            max: 150,
            description: "自由状态速度"
        },
        scaredSpeed: {
            min: 180,
            default: 200,
            max: 250,
            description: "惊吓状态速度"
        },
        scaredDuration: {
            min: 2,
            default: 3,
            max: 5,
            description: "惊吓持续时间(秒)"
        },
        clusterDistance: {
            min: 40,
            default: 50,
            max: 80,
            description: "集群判定距离"
        }
    },
    level: {
        speedIncrement: {
            min: 5,
            default: 10,
            max: 20,
            description: "每关速度增量"
        },
        clusterTimeout: {
            min: 20,
            default: 30,
            max: 45,
            description: "集群解散时间(秒)"
        }
    }
};
```

#### 调试建议流程
1. **第一轮测试**：使用默认参数完成基础功能
2. **第二轮测试**：调整猎犬速度，确保操作手感
3. **第三轮测试**：调整鸭子参数，平衡游戏难度
4. **第四轮测试**：调整关卡参数，确保难度曲线

#### 数据收集方法
```typescript
// 游戏数据收集脚本
class GameAnalytics {
    private static data = {
        levelCompletionTimes: [],
        playerActions: [],
        difficultyFeedback: []
    };

    static recordLevelCompletion(level: number, time: number) {
        this.data.levelCompletionTimes.push({
            level: level,
            time: time,
            timestamp: Date.now()
        });
    }

    static recordPlayerAction(action: string, position: Vec3) {
        this.data.playerActions.push({
            action: action,
            position: position,
            timestamp: Date.now()
        });
    }

    static exportData() {
        return JSON.stringify(this.data);
    }
}
```

### 8.2 性能优化建议

#### 渲染优化
1. **合批处理**
   - 使用相同材质的对象
   - 减少DrawCall数量
   - 合理使用图集

2. **纹理优化**
   - 压缩纹理格式
   - 合理的纹理尺寸
   - 避免过大的纹理

#### 代码优化
```typescript
// 对象池管理
class ObjectPool {
    private pool: Node[] = [];
    private prefab: Prefab;

    constructor(prefab: Prefab, initialSize: number = 10) {
        this.prefab = prefab;
        this.initPool(initialSize);
    }

    private initPool(size: number) {
        for (let i = 0; i < size; i++) {
            const obj = instantiate(this.prefab);
            obj.active = false;
            this.pool.push(obj);
        }
    }

    getObject(): Node {
        if (this.pool.length > 0) {
            const obj = this.pool.pop();
            obj.active = true;
            return obj;
        }
        return instantiate(this.prefab);
    }

    returnObject(obj: Node) {
        obj.active = false;
        this.pool.push(obj);
    }
}
```

#### 内存管理
1. **及时释放资源**
2. **避免内存泄漏**
3. **合理使用对象池**

### 8.3 常见问题解决

#### 问题1：游戏卡顿
**症状**：游戏运行不流畅，帧率低
**原因**：
- 渲染对象过多
- 脚本计算量大
- 内存使用过高

**解决方案**：
1. 使用Profiler分析性能瓶颈
2. 优化渲染批次
3. 减少不必要的计算
4. 使用对象池管理

#### 问题2：触屏响应不准确
**症状**：点击位置与猎犬移动位置不符
**原因**：
- 坐标转换错误
- 屏幕适配问题

**解决方案**：
```typescript
// 正确的2D坐标转换
onTouchStart(event: EventTouch) {
    const touchPos = event.getUILocation();
    
    // 转换屏幕坐标到世界坐标（2D版本）
    const worldPos = new Vec3();
    this.camera.screenToWorld(new Vec3(touchPos.x, touchPos.y, 0), worldPos);
    this.moveToTarget(worldPos);
}
```

#### 问题3：鸭子集群判定不准确
**症状**：鸭子明明很近但不形成集群
**原因**：
- 距离计算错误
- 状态更新时机问题

**解决方案**：
```typescript
// 改进的集群检测
private checkClusterFormation(ducks: DuckController[]) {
    for (let i = 0; i < ducks.length; i++) {
        let nearbyCount = 0;
        
        for (let j = 0; j < ducks.length; j++) {
            if (i !== j) {
                const distance = Vec3.distance(
                    ducks[i].node.position, 
                    ducks[j].node.position
                );
                
                if (distance <= this.clusterDistance) {
                    nearbyCount++;
                }
            }
        }
        
        // 需要至少2只鸭子才能形成集群
        if (nearbyCount >= Math.min(2, ducks.length - 1)) {
            ducks[i].setState(DuckState.CLUSTERED);
        }
    }
}
```

#### 问题4：微信API调用失败
**症状**：排行榜、分享等功能无效
**原因**：
- 开发环境与真机环境差异
- API权限配置问题

**解决方案**：
1. 在真机上测试微信API
2. 检查小程序配置
3. 添加错误处理机制

```typescript
// 安全的微信API调用
private safeWxCall(apiName: string, options: any) {
    if (typeof wx !== 'undefined' && wx[apiName]) {
        wx[apiName](options);
    } else {
        console.warn(`微信API ${apiName} 不可用`);
    }
}
```

---

## 总结

本文档提供了《猎犬驱鸭》微信小程序游戏的完整开发指南，包括：

1. **技术方案**：基于Cocos Creator 3.8.0的完整技术栈
2. **详细教程**：面向非程序员的详细操作步骤
3. **代码实现**：核心功能的完整代码示例
4. **发布指南**：微信小程序发布的完整流程
5. **优化建议**：性能优化和问题解决方案

按照本文档的步骤，您可以完成一个功能完整、性能良好的微信小程序游戏。建议在开发过程中：

- 严格按照时间规划执行
- 及时进行测试和调试
- 重视游戏平衡性调整
- 做好版本管理和备份

祝您开发顺利！

