{"version": 3, "file": "quat.d.ts", "sourceRoot": "", "sources": ["../../../../source/script/utils/math/quat.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AACrD,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,IAAI,CAAC;AAItC,UAAU,kBAAkB,CAAC,CAAC;IAC1B,MAAM,EAAE,MAAM,CAAC;IACf,CAAC,KAAK,EAAE,MAAM,GAAG,CAAC,CAAC;CACtB;AAaD,qBAAa,KAAK;IACd,OAAc,QAAQ,iBAA6B;IAEnD;;OAEG;WACW,KAAK,CAAC,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG;IAIjD;;OAEG;WACW,IAAI,CAAC,GAAG,SAAS,SAAS,EAAE,QAAQ,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,QAAQ;IAQ3F;;OAEG;WACW,GAAG,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM;IAQ7F;;OAEG;WACW,QAAQ,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG;IAQtD;;OAEG;WACW,UAAU,CAAC,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO;IA0B3G;;;;;OAKG;WACW,YAAY,CAAC,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,GAAG;IAgBrG;;OAEG;WACW,QAAQ,CAAC,GAAG,SAAS,SAAS,EAAE,UAAU,SAAS,SAAS,EAAE,UAAU,SAAS,SAAS,EACpG,GAAG,EAAE,GAAG,EACR,CAAC,EAAE,UAAU,EACb,CAAC,EAAE,UAAU;IAajB;;OAEG;WACW,cAAc,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM;IAQ/E;;OAEG;WACW,WAAW,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM;IAQxF;;;OAGG;WACW,OAAO,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM;IAa1E;;;OAGG;WACW,OAAO,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM;IAa1E;;;OAGG;WACW,OAAO,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM;IAa1E;;;;OAIG;WACW,YAAY,CAAC,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;IAU3H;;;;OAIG;WACW,iBAAiB,CAAC,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;IAMhI;;OAEG;WACW,UAAU,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAQhE;;OAEG;WACW,GAAG,CAAC,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAIvD;;OAEG;WACW,IAAI,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM;IAQ7E;;OAEG;WACW,KAAK,CAAC,GAAG,SAAS,SAAS,EAAE,UAAU,SAAS,SAAS,EAAE,UAAU,SAAS,SAAS,EACjG,GAAG,EAAE,GAAG,EACR,CAAC,EAAE,UAAU,EACb,CAAC,EAAE,UAAU,EACb,CAAC,EAAE,MAAM;IAwCb;;OAEG;WACW,MAAM,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM;IAO/F;;OAEG;WACW,MAAM,CAAC,GAAG,SAAS,SAAS,EAAE,QAAQ,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,QAAQ;IAa7F;;OAEG;WACW,SAAS,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAQ/D;;OAEG;WACW,GAAG,CAAC,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG;IAI/C;;OAEG;WACW,SAAS,CAAC,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG;IAIrD;;OAEG;WACW,SAAS,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAY/D;;OAEG;WACW,QAAQ,CAAC,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO;IAKjI;;;;OAIG;WACW,UAAU,CAAC,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,IAAI;IAK7G;;OAEG;WACW,aAAa,CAAC,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;IAUlH;;OAEG;WACW,QAAQ,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI;IAsC/D;;OAEG;WACW,SAAS,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM;IAoBxF;;;OAGG;WACW,UAAU,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM;IAOnE;;OAEG;WACW,OAAO,CAAC,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,EAAE,GAAG;IAU5F;;OAEG;WACW,OAAO,CAAC,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,EAAE,GAAG;IAW5F;;OAEG;WACW,OAAO,CAAC,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,EAAE,GAAG;IAW5F;;;OAGG;WACW,OAAO,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,SAAS,EAAE,MAAM,CAAC,EAAE,OAAO;IAiCrF;;;OAGG;WACW,OAAO,CAAC,GAAG,SAAS,kBAAkB,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,SAAI;IAQ7F;;;OAGG;WACW,SAAS,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,kBAAkB,CAAC,MAAM,CAAC,EAAE,GAAG,SAAI;IAQjG;;OAEG;WACW,YAAY,CAAC,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAIhE;;OAEG;WACW,MAAM,CAAC,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,OAAO,SAAU;CAQhF"}