{"version": 3, "file": "rect-gizmo.d.ts", "sourceRoot": "", "sources": ["../../../../../../../source/script/public/gizmos/3d/elements/transform/rect-gizmo.ts"], "names": [], "mappings": "AAEA,OAAO,EAAY,KAAK,EAA2B,IAAI,EAAiC,IAAI,EAAE,IAAI,EAAE,MAAM,IAAI,CAAC;AAK/G,OAAO,EAAE,mBAAmB,EAAE,MAAM,qCAAqC,CAAC;AAE1E,OAAO,EAAE,kBAAkB,EAAE,MAAM,kBAAkB,CAAC;AACtD,OAAO,EAAyB,kBAAkB,EAAE,MAAM,wCAAwC,CAAC;AAEnG,OAAO,eAAe,MAAM,gCAAgC,CAAC;AAC7D,OAAO,EAAE,mBAAmB,EAAE,cAAc,IAAI,UAAU,EAAE,MAAM,oCAAoC,CAAC;AACvG,OAAO,cAAc,MAAM,mBAAmB,CAAC;AAe/C,cAAM,SAAU,SAAQ,cAAc;IAClC,SAAS,CAAC,WAAW,EAAG,mBAAmB,CAAC;IAC5C,OAAO,CAAC,aAAa,CAAc;IACnC,OAAO,CAAC,aAAa,CAAc;IACnC,OAAO,CAAC,SAAS,CAAc;IAC/B,OAAO,CAAC,WAAW,CAAc;IACjC,OAAO,CAAC,SAAS,CAAc;IAC/B,OAAO,CAAC,YAAY,CAAqB;IACzC,OAAO,CAAC,SAAS,CAAc;IAC/B,OAAO,CAAC,SAAS,CAAc;IAC/B,OAAO,CAAC,OAAO,CAAS;IAGxB,OAAO,CAAC,aAAa,CAAc;IACnC,OAAO,CAAC,kBAAkB,CAAmB;IAC7C,OAAO,CAAC,oBAAoB,CAAmB;IAC/C,OAAO,CAAC,sBAAsB,CAAmB;IACjD,OAAO,CAAC,SAAS,CAAS;IAE1B,IAAI;IAKJ,KAAK;IAIL,kBAAkB,CAAC,IAAI,EAAE,IAAI;IAO7B,uBAAuB,CAAC,IAAI,EAAE,IAAI;IAMlC,gBAAgB;IAkBhB,qBAAqB;IA8CrB,qBAAqB;IAIrB,mBAAmB,CAAC,KAAK,EAAE,kBAAkB;IA8B7C,cAAc,CAAC,KAAK,EAAE,mBAAmB;IAMzC,YAAY,CAAC,KAAK,EAAE,mBAAmB;IAavC,cAAc,CAAC,KAAK,EAAE,IAAI;IAuC1B,gBAAgB,CAAC,KAAK,EAAE,IAAI;IA+B5B,YAAY,CAAC,IAAI,EAAE,UAAU;IA0B7B,wBAAwB,CAAC,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO;IA+BtG,eAAe,CAAC,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,IAAI;IAcjD,mBAAmB,CAAC,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO;IA2E1F,qBAAqB,CAAC,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO;IAmDxE,SAAS,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE;IA2CvD,wBAAwB;IAwBxB,yBAAyB;IAIzB,oBAAoB;IA+CpB,sBAAsB,CAAC,cAAc,EAAE,kBAAkB;IAqDzD,yBAAyB;IAQzB,0BAA0B;IAI1B,mBAAmB,CAAC,cAAc,EAAE,kBAAkB;IAoBtD,kBAAkB,CAAC,cAAc,EAAE,kBAAkB,EAAE,SAAS,EAAE,eAAe,EAAE,KAAK,kBAAY;IAyBpG,cAAc,CAAC,SAAS,EAAE,eAAe,EAAE,gBAAgB,EAAE,IAAI,EAAE,EAAE,KAAK,kBAAY;IAStF,2BAA2B;IAmB3B,4BAA4B;IAI5B,yBAAyB;IAqCzB,0BAA0B;CAG7B;AAED,eAAe,SAAS,CAAC"}