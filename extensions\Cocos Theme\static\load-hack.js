(()=>{
    var windowType = 'window/placeholder'
    var themeValue = getThemeStored()
    saveOriginalTheme()
    changeAnimatorStyle(themeValue)
    var headerColor = getHeaderColor(themeValue)
    changeInspectorHeader()
    changedAllWindowsStyle(themeValue)
    refreshWindows(themeValue)

    /* 获取插件使用后存储的主题 */
    function getThemeStored() {
        let themeDataStored = localStorage.getItem('cocos-theme-stored')
        if (!themeDataStored) {
            return null
        }

        themeDataStored = JSON.parse(themeDataStored)
        let themeName = themeDataStored.name
        // 如果主题是Default，则使用原有主题
        if (themeName == 'Default') {
            return null
        }

        let themeValue = themeDataStored.value
        return themeValue
    }

    /* 获取插件原本主题 */
    function saveOriginalTheme() {
        if (windowType.indexOf('windows/main.html') > -1) {
            // 该方法只会在插件加载时执行一次
            let originalCSS = document.head.getElementsByTagName('style')[0].textContent
            localStorage.setItem('cocos-original-theme', originalCSS)
        }
    }

    /* 刷新各个窗口 */
    function refreshWindows(themeValue) {
        if (windowType.indexOf('windows/main.html') > -1) {
            let refreshIntervalId = setInterval(()=>{
                Editor.Message.broadcast('cocos-theme:refresh-windows', [])
                changeInspectorHeader()
            }, 1000)

            localStorage.setItem('refresh-interval-id', refreshIntervalId)
        }
    }

    /* 额外需要执行的操作：改变动画编辑器的主题 */
    function changeAnimatorStyle(themeValue) {
        if (!themeValue) {
            return
        }
        
        setTimeout(()=>{
            let dockEle = document.querySelector('#dock');
            if (!dockEle) {
                return
            }
    
            let animatorEle = dockEle.shadowRoot.querySelector('dock-layout panel-frame[name=animator]')
            if (!animatorEle) {
                return
            }
            
            let bgColor = themeValue.split(';')[0].split(':')[1].trim()
            animatorEle.shadowRoot.querySelector('div.animator div#header').style.backgroundColor = bgColor
            animatorEle.shadowRoot.querySelector('div.animator div.container').style.backgroundColor = bgColor
        }, 1500) // shadowRoot可能还没有出来，所以延迟执行
    }

    /* 获取Header应该被设置的颜色，针对Cocos 3.8及之后版本*/
    function getHeaderColor(themeValue) {
        if (!themeValue) {
            return ''
        }

        let headerColor = themeValue.split('--color-normal-fill-emphasis:')[1].split(';')[0].trim()
        return headerColor
    }

    /* 额外需要执行的操作：改变属性检查器的Header，针对Cocos 3.8及之后版本 */
    function changeInspectorHeader() {
        setTimeout(()=>{
            /* 需不断执行 */
            let dockEle = document.querySelector('#dock')
            if (!dockEle) {
                return
            }

            let inspectorEle = dockEle.shadowRoot.querySelector('dock-layout panel-frame[name=inspector]')
            if (!inspectorEle) {
                return
            }
            
            let panelEle = inspectorEle.shadowRoot.querySelector('ui-panel.content').shadowRoot
            if (!panelEle) {
                return
            }
            
            let sections = panelEle.querySelectorAll('ui-drag-area.container section.body ui-section')
            for (let i=0; i<sections.length; i++) {
                sections[i].shadowRoot.querySelector('header.header').style.backgroundColor = headerColor
            }

        }, 1500)  // shadowRoot可能还没有出来，所以延迟执行
    }

    /* 改变所有窗口样式 */
    function changedAllWindowsStyle(themeValue) {
        if (!themeValue) {
            return
        }

        let styleTags = document.head.getElementsByTagName('style')
        if (styleTags.length) {
            for (let i=0; i<styleTags.length; i++) {
                styleTags[i].textContent = themeValue
            }
        }
    }

    /* 存储当前设置的主题 */
    function saveSelectedTheme(themeName, themeValue) {
        // 缓存当前设置的主题
        localStorage.setItem('cocos-theme-stored', JSON.stringify({'name': themeName, 'value': themeValue}))
    }

    /* 改变主题 */
    function changeTheme(data) {
        if (!data) {
            return
        }
        
        let themeName = data[0]
        let themeValue = null

        if (themeName == 'Default') {
            themeValue = localStorage.getItem('cocos-original-theme')
        }
        else {
            themeValue = data[1]
        }

        saveSelectedTheme(themeName, themeValue)
        changedAllWindowsStyle(themeValue)
        changeAnimatorStyle(themeValue)

        // 改变全局变量headerColor
        headerColor = getHeaderColor(themeValue)
        changeInspectorHeader(themeValue)
    }

    // /* 改变图片 */
    // function changeBg(data) {
    //     if (!data) {
    //         return
    //     }

    //     let bgPath = data[0]
    //     let opacityValue = data[1]

    //     if (bgPath == '') {
    //         // 删除背景图
    //     }
    //     else {
    //         // 设置背景图
    //     }
    // }

    function removeThemeChangedListener() {
        Editor.Message.removeBroadcastListener('cocos-theme:theme-changed', changeTheme)
    }

    // function removeBgChangedListener() {
    //     Editor.Message.removeBroadcastListener('cocos-theme:bg-changed', changeBg)
    // }

    try {
        // 开启插件，进行事件监听
        removeThemeChangedListener()
        // removeBgChangedListener()
        Editor.Message.addBroadcastListener('cocos-theme:theme-changed', changeTheme)
        // Editor.Message.addBroadcastListener('cocos-theme:bg-changed', changeBg)

        // 关停插件，移除事件监听
        Editor.Message.removeBroadcastListener('cocos-theme:remove-listener', removeThemeChangedListener)
        // Editor.Message.removeBroadcastListener('cocos-theme:remove-listener', removeBgChangedListener)
        Editor.Message.addBroadcastListener('cocos-theme:remove-listener', removeThemeChangedListener)
        // Editor.Message.addBroadcastListener('cocos-theme:remove-listener', removeBgChangedListener)
    }
    catch(e) {
        
    }
})()