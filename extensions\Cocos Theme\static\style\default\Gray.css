body {
    --color-normal-fill: #c2c2c2;
    --color-normal-fill-important: #a8a8a8;
    --color-normal-fill-emphasis: #a2a2a2;
    --color-normal-fill-normal: #c2c2c2;
    --color-normal-fill-weaker: #b9b9b9;
    --color-normal-fill-weakest: #afafaf;
    
    --color-normal-contrast: #000000;
    --color-normal-contrast-important: #000000; 
    --color-normal-contrast-emphasis: #000000;
    --color-normal-contrast-normal: #000000;
    --color-normal-contrast-weaker: #000000;
    --color-normal-contrast-weakest: #000000;

    --color-normal-border: #a2a2a2;
    --color-normal-border-important: #a2a2a2;
    --color-normal-border-emphasis: #a2a2a2;
    --color-normal-border-normal: #a2a2a2;
    --color-normal-border-weaker: #a2a2a2;
    --color-normal-border-weakest: #a2a2a2;

    --color-focus-fill: #d2d2d2;
    --color-focus-fill-important: #727272;
    --color-focus-fill-emphasis: #6c6c6c;
    --color-focus-fill-normal: #000000;
    --color-focus-fill-weaker: #0a0a0a;
    --color-focus-fill-weakest: #151515;

    --color-focus-contrast: #CCCCCC;
    --color-focus-contrast-important: #CCCCCC;
    --color-focus-contrast-emphasis: #fafafa;
    --color-focus-contrast-normal: #CCCCCC;
    --color-focus-contrast-weaker: #CCCCCC;
    --color-focus-contrast-weakest: #CCCCCC;

    --color-focus-border: #2a2a2a;
    --color-focus-border-important: #161616;
    --color-focus-border-emphasis: #ffffff;
    --color-focus-border-normal: #2a2a2a;
    --color-focus-border-weaker: #343434;
    --color-focus-border-weakest: #3e3e3e;

    --color-hover-fill: #d2d2d2;
    --color-hover-fill-important: #bcbcbc;          
    --color-hover-fill-emphasis: #c2c2c2;
    --color-hover-fill-normal: #d2d2d2;             
    --color-hover-fill-weaker: #8F8F8F;
    --color-hover-fill-weakest: #999999;

    --color-hover-contrast: #CCCCCC;
    --color-hover-contrast-important: #CCCCCC;
    --color-hover-contrast-emphasis: #CCCCCC;
    --color-hover-contrast-normal: #CCCCCC;
    --color-hover-contrast-weaker: #CCCCCC;
    --color-hover-contrast-weakest: #CCCCCC;
    
    --color-hover-border: #227F9B;
    --color-hover-border-important: #050505;
    --color-hover-border-emphasis: #227F9B;
    --color-hover-border-normal: #227F9B;
    --color-hover-border-weaker: #050505;
    --color-hover-border-weakest: #050505;

    --color-active-fill: #8f8f8f;
    --color-active-fill-important: #2b2b2b;
    --color-active-fill-emphasis: #8F8F8F;
    --color-active-fill-normal: #525252;
    --color-active-fill-weaker: #8F8F8F;
    --color-active-fill-weakest: #A3A3A3;

    --color-active-contrast: #CCCCCC;
    --color-active-contrast-important: #CCCCCC;
    --color-active-contrast-emphasis: #CCCCCC;
    --color-active-contrast-normal: #CCCCCC;
    --color-active-contrast-weaker: #CCCCCC;
    --color-active-contrast-weakest: #CCCCCC;
    --color-active-border: #525252;
    --color-active-border-important: #050505;
    --color-active-border-emphasis: #3D3D3D;
    --color-active-border-normal: #525252;
    --color-active-border-weaker: #8F8F8F;
    --color-active-border-weakest: #A3A3A3;
    --color-primary-fill: #40AACA;
    --color-primary-fill-important: #135C73;
    --color-primary-fill-emphasis: #227F9B;
    --color-primary-fill-normal: #40AACA;
    --color-primary-fill-weaker: #65BDD7;
    --color-primary-fill-weakest: #AFDFEE;
    --color-primary-contrast: #FAFAFA;
    --color-primary-contrast-important: #FAFAFA;
    --color-primary-contrast-emphasis: #FAFAFA;
    --color-primary-contrast-normal: #FAFAFA;
    --color-primary-contrast-weaker: #FAFAFA;
    --color-primary-contrast-weakest: #000000;
    --color-primary-border: #40AACA;
    --color-primary-border-important: #135C73;
    --color-primary-border-emphasis: #227F9B;
    --color-primary-border-normal: #40AACA;
    --color-primary-border-weaker: #65BDD7;
    --color-primary-border-weakest: #AFDFEE;
    --color-icon-fill: #40AACA;
    --color-icon-fill-important: #135C73;
    --color-icon-fill-emphasis: #227F9B;
    --color-icon-fill-normal: #40AACA;
    --color-icon-fill-weaker: #65BDD7;
    --color-icon-fill-weakest: #AFDFEE;
    --color-icon-contrast: #FAFAFA;
    --color-icon-contrast-important: #FAFAFA;
    --color-icon-contrast-emphasis: #FAFAFA;
    --color-icon-contrast-normal: #FAFAFA;
    --color-icon-contrast-weaker: #FAFAFA;
    --color-icon-contrast-weakest: #000000;
    --color-icon-border: #40AACA;
    --color-icon-border-important: #135C73;
    --color-icon-border-emphasis: #227F9B;
    --color-icon-border-normal: #40AACA;
    --color-icon-border-weaker: #65BDD7;
    --color-icon-border-weakest: #AFDFEE;

    --color-default-fill: #d2d2d2;
    --color-default-fill-important: #bcbcbc;
    --color-default-fill-emphasis: #c2c2c2;
    --color-default-fill-normal: #d2d2d2;
    --color-default-fill-weaker: #8F8F8F;
    --color-default-fill-weakest: #999999;

    --color-default-contrast: #000000;
    --color-default-contrast-important: #000000;
    --color-default-contrast-emphasis: #000000;
    --color-default-contrast-normal: #000000;
    --color-default-contrast-weaker: #000000;
    --color-default-contrast-weakest: #000000;

    --color-default-border: #3d3d3d;
    --color-default-border-important: #3D3D3D;
    --color-default-border-emphasis: #2b2b2b;
    --color-default-border-normal: #3d3d3d;
    --color-default-border-weaker: #8f8f8f;
    --color-default-border-weakest: #999999;

    --color-success-fill: #198F6B;
    --color-success-fill-important: #198F6B;
    --color-success-fill-emphasis: #32B086;
    --color-success-fill-normal: #64DAAF;
    --color-success-fill-weaker: #8CE3BD;
    --color-success-fill-weakest: #aaffdb;
    --color-success-contrast: #fafafa;
    --color-success-contrast-important: #fafafa;
    --color-success-contrast-emphasis: #fafafa;
    --color-success-contrast-normal: #fafafa;
    --color-success-contrast-weaker: #fafafa;
    --color-success-contrast-weakest: #fafafa;
    --color-success-border: #64DAAF;
    --color-success-border-important: #198F6B;
    --color-success-border-emphasis: #32B086;
    --color-success-border-normal: #64DAAF;
    --color-success-border-weaker: #8CE3BD;
    --color-success-border-weakest: #aaffdb;
    --color-info-fill: #227F9B;
    --color-info-fill-important: #6c6c6c;
    --color-info-fill-emphasis: #135C73;
    --color-info-fill-normal: #227F9B;
    --color-info-fill-weaker: #40AACA;
    --color-info-fill-weakest: #65BDD7;
    --color-info-contrast: #FAFAFA;
    --color-info-contrast-important: #FAFAFA;
    --color-info-contrast-emphasis: #FAFAFA;
    --color-info-contrast-normal: #FAFAFA;
    --color-info-contrast-weaker: #FAFAFA;
    --color-info-contrast-weakest: #FAFAFA;
    --color-info-border: #40AACA;
    --color-info-border-important: #135C73;
    --color-info-border-emphasis: #227F9B;
    --color-info-border-normal: #40AACA;
    --color-info-border-weaker: #65BDD7;
    --color-info-border-weakest: #AFDFEE;
    --color-danger-fill: #D85F6F;
    --color-danger-fill-important: #8D1833;
    --color-danger-fill-emphasis: #AE2D47;
    --color-danger-fill-normal: #D85F6F;
    --color-danger-fill-weaker: #E27F88;
    --color-danger-fill-weakest: #ff9ca4;
    --color-danger-contrast: #fafafa;
    --color-danger-contrast-important: #fafafa;
    --color-danger-contrast-emphasis: #fafafa;
    --color-danger-contrast-normal: #fafafa;
    --color-danger-contrast-weaker: #fafafa;
    --color-danger-contrast-weakest: #ffffff;
    --color-danger-border: #D85F6F;
    --color-danger-border-important: #8D1833;
    --color-danger-border-emphasis: #AE2D47;
    --color-danger-border-normal: #D85F6F;
    --color-danger-border-weaker: #E27F88;
    --color-danger-border-weakest: #ff9ca4;
    --color-warn-fill: #F1A348;
    --color-warn-fill-important: #753307;
    --color-warn-fill-emphasis: #C66F23;
    --color-warn-fill-normal: #F1A348;
    --color-warn-fill-weaker: #F5BB70;
    --color-warn-fill-weakest: #ffd98c;
    --color-warn-contrast: #fafafa;
    --color-warn-contrast-important: #fafafa;
    --color-warn-contrast-emphasis: #fafafa;
    --color-warn-contrast-normal: #fafafa;
    --color-warn-contrast-weaker: #fafafa;
    --color-warn-contrast-weakest: #ffffff;
    --color-warn-border: #F1A348;
    --color-warn-border-important: #753307;
    --color-warn-border-emphasis: #C66F23;
    --color-warn-border-normal: #F1A348;
    --color-warn-border-weaker: #F5BB70;
    --color-warn-border-weakest: #ffd98c;
    --color-selection-fill: #0966d5;
    --color-selection-fill-normal: #0966d5;
    --color-selection-fill-weaker: #4781f4;
    --color-selection-fill-weakest: #6d9dff;
    --color-selection-fill-emphasis: #004db7;
    --color-selection-fill-important: #003599;
    --color-selection-contrast: #ffffff;
    --color-selection-contrast-normal: #ffffff;
    --color-selection-contrast-weaker: #ffffff;
    --color-selection-contrast-weakest: #ffffff;
    --color-selection-contrast-emphasis: #e0e0e0;
    --color-selection-contrast-important: #c2c2c2;
    --size-normal-line: 24;
    --size-normal-font: 12;
    --size-normal-radius: 2;
    --size-normal-border: 1;
    --size-large-line: 32;
    --size-large-font: 16;
    --size-large-radius: 2;
    --size-large-border: 1;
    --size-big-line: 28;
    --size-big-font: 14;
    --size-big-radius: 2;
    --size-big-border: 1;
    --size-small-line: 20;
    --size-small-font: 10;
    --size-small-radius: 2;
    --size-small-border: 1;
    --size-little-line: 16;
    --size-little-font: 8;
    --size-little-radius: 2;
    --size-little-border: 1;
    --font-normal: BlinkMacSystemFont,'Helvetica Neue',Helvetica,'Lucida Grande','Segoe UI',Ubuntu,Cantarell,SourceHanSansCN-Normal,Arial,sans-serif;
    --font-editor: Menlo,Monaco,"Courier New",monospace;
  }