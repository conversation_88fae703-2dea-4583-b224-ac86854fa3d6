{"version": 3, "file": "gizmo-manager.d.ts", "sourceRoot": "", "sources": ["../../../../../source/script/public/gizmos/3d/gizmo-manager.ts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,kBAAkB,EAAE,MAAM,+BAA+B,CAAC;AAEnE,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAO,EAAiB,SAAS,EAAsB,eAAe,EAAE,IAAI,EAAE,KAAK,EAAQ,MAAM,EAAE,MAAM,IAAI,CAAC;AAK9G,OAAO,EACH,iBAAiB,EAEjB,WAAW,EACX,6BAA6B,EAC7B,+BAA+B,EAC/B,0BAA0B,EAC7B,MAAM,8BAA8B,CAAC;AAEtC,OAAO,SAAS,MAAM,uBAAuB,CAAC;AAI9C,OAAO,EAAE,YAAY,EAAE,MAAM,4CAA4C,CAAC;AAC1E,OAAO,aAAa,MAAM,uCAAuC,CAAC;AAGlE,OAAO,EAAE,mBAAmB,EAAyB,MAAM,kCAAkC,CAAC;AAE9F,OAAO,0BAA0B,MAAM,oDAAoD,CAAC;AAG5F,OAAO,cAAc,MAAM,sCAAsC,CAAC;AAClE,OAAO,eAAe,MAAM,+CAA+C,CAAC;AAC5E,OAAO,EAAE,SAAS,EAAE,MAAM,8CAA8C,CAAC;AAQzE,OAAO,QAAQ,IAAI,CAAC;IAChB,UAAiB,IAAI;QACjB,KAAK,EAAE,SAAS,GAAG,IAAI,CAAC;QACxB,SAAS,EAAE,aAAa,GAAG,IAAI,CAAC;QAChC,eAAe,EAAE,SAAS,GAAG,IAAI,CAAC;QAClC,mBAAmB,CAAC,EAAE,OAAO,CAAA;KAChC;IAED,UAAiB,SAAS;QACtB,KAAK,EAAE,SAAS,GAAG,IAAI,CAAC;QACxB,SAAS,EAAE,aAAa,GAAG,IAAI,CAAC;QAChC,eAAe,EAAE,SAAS,GAAG,IAAI,CAAC;KACrC;CAEJ;AA4CD,qBAAa,YAAa,SAAQ,YAAa,YAAW,YAAY,EAAE,0BAA0B;IAC9F,gBAAuB,sBAAsB,uBAAuB;IAE7D,iBAAiB,EAAE,iBAAiB,CAAqB;IAChE,OAAO,CAAC,UAAU,CAAC,CAAS;IAC5B,OAAO,CAAC,cAAc,CAAC,CAAO;IAC9B,OAAO,CAAC,mBAAmB,CAAQ;IACnC,OAAO,CAAC,oBAAoB,CAAoC;IAChE,OAAO,CAAC,iBAAiB,CAAuB;IAEhD,OAAO,CAAC,aAAa,CAAqB;IAG1C,OAAO,CAAC,aAAa,CAAW;IAChC,OAAO,CAAC,UAAU,CAAgB;IAClC,OAAO,CAAC,WAAW,CAAW;IAC9B,OAAO,CAAC,cAAc,CAAW;IACjC,OAAO,CAAC,oBAAoB,CAAW;IAEvC,OAAO,CAAC,cAAc,CAA+B;IAErD;;;;;;;OAOG;IACI,2BAA2B,EAAE,0BAA0B,EAAE,CAAoB;IACpF,OAAO,CAAC,mBAAmB,CAAS;IACpC,OAAO,CAAC,8BAA8B,CAAS;IAExC,aAAa;IAIb,oBAAoB,CAAC,IAAI,EAAE,6BAA6B;IAOxD,eAAe;IAIf,aAAa,CAAC,IAAI,EAAE,+BAA+B;IAOnD,UAAU;IAIV,QAAQ,CAAC,IAAI,EAAE,0BAA0B;IAOzC,SAAS;IAIT,OAAO,CAAC,KAAK,EAAE,OAAO;IAetB,kBAAkB;IAIlB,mBAAmB,CAAC,KAAK,EAAE,OAAO;IAiBlC,aAAa;IAIb,cAAc,CAAC,KAAK,EAAE,OAAO;IAqB7B,kBAAkB;IAIlB,gBAAgB,CAAC,IAAI,EAAE,MAAM;IAqB7B,yBAAyB;IAIzB,uBAAuB,CAAC,IAAI,EAAE,MAAM,CAAC,WAAW,CAAC,EAAE,KAAK,EAAE,GAAG;IAI7D,wBAAwB,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,mBAAmB,CAAC;IAS3D,sBAAsB,CAAC,IAAI,EAAE,MAAM,CAAC,mBAAmB,CAAC,EAAE,KAAK,EAAE,GAAG;IAKpE,IAAI;IA+BE,cAAc;IAoBpB,mBAAmB;IAMnB,gBAAgB;IAoBhB,uBAAuB;IAYvB,aAAa;IAuBb,aAAa;IAIb,aAAa;IAYpB,IAAI,aAAa,0BAEhB;IAED,IAAI,iBAAiB,kCAEpB;IAED,IAAI,iBAAiB,CAAC,QAAQ,+BAAA,EAe7B;IAED,IAAI,UAAU,oCAEb;IAED,IAAI,UAAU,CAAC,KAAK,iCAAA,EAMnB;IAED,IAAI,KAAK,+BAER;IAED,IAAI,KAAK,CAAC,KAAK,4BAAA,EAMd;IAEM,aAAa,CAAC,KAAK,EAAE,OAAO;IAI5B,iBAAiB;IAIjB,kBAAkB,CAAC,QAAQ,EAAE,UAAU,GAAG,UAAU,GAAG,OAAO,GAAG,MAAM;IAgBvE,kBAAkB,CAAC,IAAI,EAAE,GAAG;IAU5B,cAAc;IAMd,mBAAmB,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,GAAE,GAAU;IA2C9E,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,GAAE,GAAU;IAK3D,YAAY,CAAC,KAAK,EAAE,SAAS;IAe7B,SAAS,CAAC,KAAK,EAAE,SAAS,GAAG,IAAI,GAAG,SAAS;IAM7C,kBAAkB,CAAC,IAAI,EAAE,SAAS;IAOlC,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,GAAE,GAAU;IAI/D,qBAAqB,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,GAAE,GAAU;IAIrE,kBAAkB,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,UAAQ;IAehD,mBAAmB,CAAC,IAAI,EAAE,IAAI;IA2B9B,kBAAkB,CAAC,SAAS,EAAE,SAAS;IAgBvC,sBAAsB,CAAC,SAAS,EAAE,SAAS;IAoB3C,4BAA4B,CAAC,SAAS,EAAE,SAAS;IAgBjD,4BAA4B,CAAC,IAAI,EAAE,IAAI;IAevC,kCAAkC,CAAC,IAAI,EAAE,IAAI;IAe7C,4BAA4B,CAAC,IAAI,EAAE,IAAI;IAmBvC,oBAAoB,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,UAAQ;IAelD,qBAAqB,CAAC,IAAI,EAAE,IAAI;IAahC,oBAAoB,CAAC,SAAS,EAAE,SAAS;IASzC,wBAAwB,CAAC,SAAS,EAAE,SAAS;IAS7C,8BAA8B,CAAC,SAAS,EAAE,SAAS;IAQnD,2BAA2B,CAAC,IAAI,EAAE,IAAI;IAMtC,+BAA+B,CAAC,IAAI,EAAE,IAAI;IAM1C,8BAA8B,CAAC,IAAI,EAAE,IAAI;IASzC,kBAAkB,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO;IA6B7C,mBAAmB,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;IA6B5E,mBAAmB;IAKnB,iBAAiB,CAAC,EAAE,EAAE,MAAM;IAW5B,kBAAkB,CAAC,EAAE,EAAE,MAAM;IAYpC;;;;OAIG;IACI,iBAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG;IAwBhD;;;;OAIG;IACI,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE;IAkC1C;;;OAGG;IACI,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE;IA6CtB,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE;IAanB,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE;IA0BlB,YAAY;IAIZ,sBAAsB,CAAC,CAAC,SAAS,MAAM,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,SAAS,EAAE,GAAG,MAAM,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO;IActI,iBAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO;IAuBhD,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,kBAAkB;IAyFlD,WAAW,CAAC,IAAI,EAAE,IAAI;IAatB,aAAa,CAAC,IAAI,EAAE,IAAI;IAMxB,gBAAgB,CAAC,IAAI,EAAE,SAAS;IAUhC,kBAAkB,CAAC,IAAI,EAAE,SAAS;IAQlC,QAAQ;IAIR,QAAQ,CAAC,SAAS,EAAE,MAAM;IAqB1B,eAAe;IAOf,+BAA+B,CAAC,UAAU,EAAE,MAAM;IAIzD,OAAO,CAAC,0BAA0B;IAe3B,WAAW,IAAI,MAAM;IAYrB,kCAAkC,CAAC,QAAQ,EAAE,0BAA0B;IAI9E;;;OAGG;IACI,yBAAyB,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI;IAIrD,IAAI,kBAAkB,gBASrB;IACM,8BAA8B,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,eAAe,KAAK,IAAI,EAAE,eAAe,EAAE,eAAe;IAUvG,gCAAgC;IAQhC,qBAAqB;IAIrB,0BAA0B,CAAC,IAAI,EAAE,OAAO;IAKxC,8BAA8B;IAI9B,2BAA2B;IAI3B,oBAAoB;CAG9B;;AAuDD,wBAAkC"}