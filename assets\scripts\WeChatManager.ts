import { _decorator, Component, sys } from 'cc';
const { ccclass } = _decorator;

@ccclass('WeChatManager')
export class WeChatManager extends Component {
    private static instance: WeChatManager = null;

    public static getInstance(): WeChatManager {
        return WeChatManager.instance;
    }

    onLoad() {
        WeChatManager.instance = this;
        this.initWeChat();
    }

    private initWeChat() {
        if (sys.platform === sys.Platform.WECHAT_GAME) {
            // 监听微信小游戏生命周期
            wx.onShow(() => {
                console.log('游戏显示');
                // 恢复游戏音效等
            });

            wx.onHide(() => {
                console.log('游戏隐藏');
                // 暂停游戏，保存进度
            });

            // 获取系统信息
            wx.getSystemInfo({
                success: (res) => {
                    console.log('设备信息：', res);
                }
            });
        }
    }

    // 分享功能
    public shareGame(title: string = '快来挑战猎犬驱鸭！') {
        if (typeof wx !== 'undefined') {
            wx.shareAppMessage({
                title: title,
                imageUrl: 'images/share.jpg' // 分享图片
            });
        }
    }

    // 显示排行榜
    public showRankList() {
        if (typeof wx !== 'undefined') {
            wx.showOpenData({
                templateId: 'rankList',
                style: {
                    left: 0,
                    top: 0,
                    width: 750,
                    height: 1334
                }
            });
        }
    }

    // 上传分数
    public uploadScore(score: number, level: number) {
        if (typeof wx !== 'undefined') {
            wx.setUserCloudStorage({
                KVDataList: [{
                    key: 'best_time',
                    value: JSON.stringify({
                        score: score,
                        level: level,
                        timestamp: Date.now()
                    })
                }],
                success: () => {
                    console.log('分数上传成功');
                },
                fail: (err) => {
                    console.error('分数上传失败：', err);
                }
            });
        }
    }

    // 获取好友排行榜数据
    public getFriendRankData() {
        if (typeof wx !== 'undefined') {
            wx.getFriendCloudStorage({
                keyList: ['best_time'],
                success: (res) => {
                    console.log('好友排行榜数据：', res.data);
                }
            });
        }
    }
}