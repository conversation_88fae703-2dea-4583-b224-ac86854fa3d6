<div id="app">
    <div id="selection">
        <div v-for="(item, index) in themesArray" class="theme-choice">
            <div class="color-div" :style="{backgroundColor: item.value}"></div>
            <ui-checkbox :value="item.name==currentTheme" :readonly="item.name==currentTheme" @change="onThemeChanged(item.name)">{{ item.name }}</ui-checkbox>
        </div>
    </div>
</div>
    <!-- <ui-section header="i18n:cocos-theme.changeTheme" class="section">

    </ui-section> -->
    <!-- <ui-section header="i18n:cocos-theme.addBg" class="section" expand>
        <div id="background">
            <div class="form-line">
                <ui-label value="i18n:cocos-theme.bgPath"></ui-label>
                &nbsp;
                <ui-file ref="file" type="file" style="width: 80%;" @change="onFileChanged($event.target.value)" placeholder="i18n:cocos-theme.selectPic" extensions="png,jpeg,jpg,gif"></ui-file>
            </div>
            <div class="form-line">
                <ui-label value="i18n:cocos-theme.bgOpacity"></ui-label>
                &nbsp;
                <ui-slider step="0.01" :value="opacityValue" min="0" max="1" style="width: 80%;" @click="onOpacityChanged($event.target.value)"></ui-slider>
            </div>
            <div class="btns-on-right">
                <ui-button type="danger" @click="deleteBg"><ui-label value="i18n:cocos-theme.deleteBg"></ui-label></ui-button>
                &nbsp;&nbsp;
                <ui-button type="primary" @click="setBg"><ui-label value="i18n:cocos-theme.setBg"></ui-label></ui-button>
            </div>
        </div>
    </ui-section> -->
    <!-- <ui-button @click="goToCustomizationPage" style="width: 100px; padding-left: 2px; padding-right: 2px;"><ui-icon value="chunk" color="true"></ui-icon>&nbsp;<ui-label value="i18n:cocos-theme.customize"></ui-label></ui-button>
        <br> -->
    <!-- <div v-else id="customization">

    </div> -->
