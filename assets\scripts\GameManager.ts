import { _decorator, Component, Node, Prefab, instantiate, Vec3 } from 'cc';
import { <PERSON><PERSON><PERSON>roll<PERSON>, DuckState } from './DuckController';
import { DogController } from './DogController';

const { ccclass, property } = _decorator;

@ccclass('GameManager')
export class GameManager extends Component {
    @property(Prefab)
    public duckPrefab: Prefab = null;
    
    @property(Prefab)
    public dogPrefab: Prefab = null;
    
    @property
    public currentLevel: number = 1;
    
    private ducks: DuckController[] = [];
    private dog: DogController = null;
    private gameStartTime: number = 0;

    start() {
        this.initializeLevel();
        this.gameStartTime = Date.now();
    }

    initializeLevel() {
        this.createDog();
        this.createDucks();
    }

    createDog() {
        const dogNode = instantiate(this.dogPrefab);
        dogNode.setPosition(0, -500, 0);
        this.node.addChild(dogNode);
        this.dog = dogNode.getComponent(Dog<PERSON>ontroller);
    }

    createDucks() {
        const levelConfig = this.getLevelConfig(this.currentLevel);
        
        for (let colorIndex = 0; colorIndex < levelConfig.colors; colorIndex++) {
            const color = this.getColorName(colorIndex);
            
            for (let i = 0; i < levelConfig.ducksPerColor; i++) {
                const duckNode = instantiate(this.duckPrefab);
                const randomPos = this.getRandomPosition();
                duckNode.setPosition(randomPos);
                
                const duckController = duckNode.getComponent(DuckController);
                duckController.duckColor = color;
                
                this.node.addChild(duckNode);
                this.ducks.push(duckController);
            }
        }
    }

    private getLevelConfig(level: number) {
        if (level <= 2) return { colors: 2, ducksPerColor: 3 + level };
        if (level <= 4) return { colors: 3, ducksPerColor: 3 + (level - 2) };
        return { colors: Math.min(4 + Math.floor((level - 5) / 2), 6), ducksPerColor: 4 };
    }

    private getColorName(index: number): string {
        const colors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange'];
        return colors[index];
    }

    private getRandomPosition(): Vec3 {
        const x = (Math.random() - 0.5) * 600;
        const y = (Math.random() - 0.5) * 800;
        return new Vec3(x, y, 0);
    }

    update() {
        this.checkCollisions();
        this.checkClusters();
        this.checkWinCondition();
    }

    private checkCollisions() {
        const dogPos = this.dog.node.position;
        
        this.ducks.forEach(duck => {
            const distance = Vec3.distance(dogPos, duck.node.position);
            if (distance < 80 && duck.getState() !== DuckState.SCARED) {
                duck.setState(DuckState.SCARED);
            }
        });
    }

    private checkClusters() {
        const colorGroups = this.groupDucksByColor();
        
        Object.keys(colorGroups).forEach(color => {
            const ducksOfColor = colorGroups[color];
            this.updateClusterState(ducksOfColor);
        });
    }

    private groupDucksByColor() {
        const groups = {};
        this.ducks.forEach(duck => {
            if (!groups[duck.duckColor]) {
                groups[duck.duckColor] = [];
            }
            groups[duck.duckColor].push(duck);
        });
        return groups;
    }

    private updateClusterState(ducks: DuckController[]) {
        // 检查是否形成集群的逻辑
        let clusteredCount = 0;
        
        for (let i = 0; i < ducks.length; i++) {
            let nearbyCount = 0;
            for (let j = 0; j < ducks.length; j++) {
                if (i !== j) {
                    const distance = Vec3.distance(ducks[i].node.position, ducks[j].node.position);
                    if (distance < 50) {
                        nearbyCount++;
                    }
                }
            }
            
            if (nearbyCount >= ducks.length - 1) {
                ducks[i].setState(DuckState.CLUSTERED);
                clusteredCount++;
            }
        }
    }

    private checkWinCondition() {
        const colorGroups = this.groupDucksByColor();
        let allClustered = true;
        
        Object.keys(colorGroups).forEach(color => {
            const ducksOfColor = colorGroups[color];
            const clusteredDucks = ducksOfColor.filter(duck => duck.getState() === DuckState.CLUSTERED);
            
            if (clusteredDucks.length !== ducksOfColor.length) {
                allClustered = false;
            }
        });
        
        if (allClustered) {
            this.onLevelComplete();
        }
    }

    private onLevelComplete() {
        const completionTime = Date.now() - this.gameStartTime;
        console.log(`关卡完成！用时：${completionTime}ms`);
        
        // 上传分数到微信排行榜
        this.uploadScore(completionTime);
        
        // 进入下一关
        this.currentLevel++;
        this.initializeLevel();
    }

    private uploadScore(time: number) {
        // 微信排行榜上传逻辑
        if (typeof wx !== 'undefined') {
            wx.setUserCloudStorage({
                KVDataList: [{
                    key: `level_${this.currentLevel}_time`,
                    value: time.toString()
                }],
                success: () => {
                    console.log('分数上传成功');
                }
            });
        }
    }
}