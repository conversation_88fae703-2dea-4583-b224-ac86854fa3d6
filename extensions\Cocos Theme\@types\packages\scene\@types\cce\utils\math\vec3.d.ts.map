{"version": 3, "file": "vec3.d.ts", "sourceRoot": "", "sources": ["../../../../source/script/utils/math/vec3.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,IAAI,CAAC;AAC1B,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAG3E,UAAU,kBAAkB,CAAC,CAAC;IAC1B,MAAM,EAAE,MAAM,CAAC;IACf,CAAC,KAAK,EAAE,MAAM,GAAG,CAAC,CAAC;CACtB;AASD,qBAAa,KAAK;IACd,OAAc,MAAM,iBAAoC;IACxD,OAAc,MAAM,iBAAoC;IACxD,OAAc,MAAM,iBAAoC;IACxD,OAAc,IAAI,iBAAoC;IACtD,OAAc,GAAG,iBAAoC;IACrD,OAAc,OAAO,iBAAuC;IAE5D;;OAEG;WACW,IAAI,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG;IAOlD;;OAEG;WACW,KAAK,CAAC,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG;IAIjD;;OAEG;WACW,IAAI,CAAC,GAAG,SAAS,SAAS,EAAE,QAAQ,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,QAAQ;IAO3F;;OAEG;WACW,GAAG,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM;IAOlF;;OAEG;WACW,GAAG,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAOjE;;OAEG;WACW,QAAQ,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAOtE;;OAEG;WACW,QAAQ,CAAC,GAAG,SAAS,SAAS,EAAE,UAAU,SAAS,SAAS,EAAE,UAAU,SAAS,SAAS,EACpG,GAAG,EAAE,GAAG,EACR,CAAC,EAAE,UAAU,EACb,CAAC,EAAE,UAAU;IAQjB;;OAEG;WACW,MAAM,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAOpE;;OAEG;WACW,IAAI,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAO1D;;OAEG;WACW,KAAK,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAO3D;;OAEG;WACW,GAAG,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAOjE;;OAEG;WACW,GAAG,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAOjE;;OAEG;WACW,KAAK,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAO3D;;OAEG;WACW,cAAc,CAAC,GAAG,SAAS,SAAS,EAAE,QAAQ,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,MAAM;IAOhH;;OAEG;WACW,WAAW,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM;IAOxF;;OAEG;WACW,QAAQ,CAAC,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAO5D;;OAEG;WACW,eAAe,CAAC,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAOnE;;OAEG;WACW,GAAG,CAAC,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG;IAO/C;;OAEG;WACW,SAAS,CAAC,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG;IAOrD;;OAEG;WACW,MAAM,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAO5D;;OAEG;WACW,MAAM,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAO5D;;OAEG;WACW,UAAU,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IA0BhE;;OAEG;WACW,SAAS,CAAC,GAAG,SAAS,SAAS,EAAE,QAAQ,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,QAAQ;IAehG;;OAEG;WACW,GAAG,CAAC,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAIvD;;OAEG;WACW,KAAK,CAAC,GAAG,SAAS,SAAS,EACrC,GAAG,EAAE,GAAG,EACR,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,EACtB,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC;IAU1B;;OAEG;WACW,IAAI,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM;IAO7E;;;OAGG;WACW,MAAM,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,EAAE,MAAM;IAapE;;OAEG;WACW,aAAa,CAAC,GAAG,SAAS,SAAS,EAAE,QAAQ,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EACpG,GAAG,EAAE,GAAG,EACR,CAAC,EAAE,QAAQ,EACX,CAAC,EAAE,OAAO;IAad;;OAEG;WACW,mBAAmB,CAAC,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,OAAO;IAYhH;;OAEG;WACW,aAAa,CAAC,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,OAAO;IAU1G;;OAEG;WACW,eAAe,CAAC,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EACrG,GAAG,EAAE,GAAG,EACR,CAAC,EAAE,OAAO,EACV,CAAC,EAAE,OAAO;IAWd;;OAEG;WACW,aAAa,CAAC,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,QAAQ,SAAS,SAAS,EACpG,GAAG,EAAE,GAAG,EACR,CAAC,EAAE,OAAO,EACV,CAAC,EAAE,QAAQ;IAiBf;;OAEG;WACW,YAAY,CAAC,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,QAAQ,SAAS,SAAS,EACnG,GAAG,EAAE,GAAG,EACR,CAAC,EAAE,OAAO,EACV,CAAC,EAAE,QAAQ,EACX,CAAC,EAAE,OAAO,EACV,CAAC,EAAE,OAAO;IAed;;OAEG;WACW,mBAAmB,CAAC,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,QAAQ,SAAS,SAAS,EAC1G,GAAG,EAAE,GAAG,EACR,CAAC,EAAE,OAAO,EACV,CAAC,EAAE,QAAQ,EACX,CAAC,EAAE,OAAO,EACV,CAAC,EAAE,OAAO;IAed;;;;;OAKG;WACW,OAAO,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM;IAqBhF;;;;;OAKG;WACW,OAAO,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM;IAqBhF;;;;;OAKG;WACW,OAAO,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM;IAqBhF;;;OAGG;WACW,OAAO,CAAC,GAAG,SAAS,kBAAkB,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,SAAI;IAQ7F;;;OAGG;WACW,SAAS,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,kBAAkB,CAAC,MAAM,CAAC,EAAE,GAAG,SAAI;IAOjG;;OAEG;WACW,YAAY,CAAC,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAIhE;;OAEG;WACW,MAAM,CAAC,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,OAAO,SAAU;IAU7E;;OAEG;WACW,KAAK,CAAC,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAazD;;;;OAIG;WACW,cAAc,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAI5E;;;;OAIG;WACW,OAAO,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;CAQxE"}