{"version": 3, "file": "controller-base.d.ts", "sourceRoot": "", "sources": ["../../../../../../../source/script/public/gizmos/3d/elements/controller/controller-base.ts"], "names": [], "mappings": "AAMA,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,IAAI,CAAC;AACnD,OAAO,EAAE,kBAAkB,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAenE,cAAM,cAAc;IAChB,IAAI,IAAI,YAEP;IAED,IAAI,OAAO,WAEV;IAED,IAAI,OAAO,YAEV;IAED,IAAI,OAAO,wBAEV;IACM,KAAK,EAAE,IAAI,GAAG,IAAI,CAAQ;IACjC,8BAA8B;IACvB,MAAM,UAAS;IAEf,qBAAqB,CAAC,CAAC,KAAK,EAAE,kBAAkB,GAAG,IAAI;IACvD,qBAAqB,CAAC,CAAC,KAAK,EAAE,kBAAkB,GAAG,IAAI;IACvD,mBAAmB,CAAC,CAAC,KAAK,EAAE,kBAAkB,GAAG,IAAI;IACrD,mBAAmB,CAAC,CAAC,KAAK,EAAE,kBAAkB,GAAG,IAAI;IACrD,oBAAoB,CAAC,CAAC,KAAK,EAAE,kBAAkB,GAAG,IAAI;IAC7D,IAAW,WAAW,YAErB;IACD,SAAS,CAAC,QAAQ,UAAS;IAC3B,SAAS,CAAC,MAAM,EAAE,IAAI,CAAqB;IAC3C,SAAS,CAAC,SAAS,EAAE,IAAI,CAAc;IACvC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAc;IACvC,SAAS,CAAC,SAAS,EAAE,IAAI,GAAG,IAAI,CAAQ;IACxC,SAAS,CAAC,SAAS,SAAO;IAC1B,SAAS,CAAC,cAAc,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,WAAW,CAAA;KAAE,CAAM;IAC9D,SAAS,CAAC,MAAM,SAAe;IAC/B,SAAS,CAAC,OAAO,SAAe;IAChC,SAAS,CAAC,qBAAqB,SAAiB;IAChD,SAAS,CAAC,iBAAiB,UAAS;IACpC,SAAS,CAAC,YAAY,UAAS;IAC/B,SAAS,CAAC,MAAM,EAAE,KAAK,CAAe;IACtC,SAAS,CAAC,SAAS,UAAS;IAE5B,OAAO,CAAC,mBAAmB,CAAyB;IACpD,OAAO,CAAC,iBAAiB,CAAyB;IAClD,OAAO,CAAC,0BAA0B,CAAyB;IAC3D,OAAO,CAAC,mBAAmB,CAAwC;IACnE,OAAO,CAAC,2BAA2B,CAAyB;IAE5D,OAAO,CAAC,eAAe,CAAsD;IAC7E,OAAO,CAAC,eAAe,CAAsD;IAC7E,OAAO,CAAC,aAAa,CAAsD;IAC3E,OAAO,CAAC,gBAAgB,CAAsD;IAC9E,OAAO,CAAC,aAAa,CAAsD;IAC3E,OAAO,CAAC,cAAc,CAAsD;gBAEhE,QAAQ,EAAE,IAAI;IAI1B,IAAW,QAAQ,CAAC,KAAK,EAAE,OAAO,EAEjC;IAED;;;OAGG;IACI,OAAO,CAAC,QAAQ,EAAE,IAAI;IAOtB,eAAe,CAAC,IAAI,EAAE,MAAM;IAK5B,cAAc;IAiBd,gBAAgB;IAmBhB,wBAAwB;IAIxB,yBAAyB;IAIzB,6BAA6B;IAM7B,+BAA+B;IAK/B,+BAA+B;IAK/B,iCAAiC;IAMjC,mBAAmB;IAInB,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM;IA2BzC,YAAY,CAAC,UAAU,EAAE,MAAM;IAW/B,cAAc,CAAC,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,MAAM;IAcjE,gBAAgB;IAkBhB,mBAAmB,CAAC,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM;IAyEnD,oBAAoB,CAAC,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM;IASpD,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC;IAMjC,WAAW,CAAC,GAAG,CAAC,EAAE,IAAI;IAStB,gBAAgB,CAAC,GAAG,CAAC,EAAE,IAAI;IAI3B,uBAAuB,CAAC,MAAM,CAAC,EAAE,IAAI,GAAG,IAAI,EAAE,GAAG,CAAC,EAAE,IAAI;IAQxD,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC;IAKjC,WAAW,CAAC,GAAG,CAAC,EAAE,IAAI;IAQtB,QAAQ;IAGR,QAAQ,CAAC,KAAK,EAAE,IAAI;IAKpB,gBAAgB;IAIhB,mBAAmB,CAAC,GAAG,EAAE,IAAI;IAQpC,SAAS,CAAC,oBAAoB;IAU9B,SAAS,CAAC,eAAe;IAIlB,aAAa,CAAC,IAAI,CAAC,EAAE,IAAI;IAczB,oBAAoB;IAWpB,UAAU,CAAC,IAAI,EAAE,IAAI;IASrB,gBAAgB,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE;IAepC,iBAAiB,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE;IAcrC,oBAAoB,CAAC,QAAQ,EAAE,IAAI;IAUnC,eAAe,CAAC,QAAQ,EAAE,IAAI,GAAG,IAAI;IAUrC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,GAAG,IAAI;IAQzC,YAAY,CAAC,QAAQ,EAAE,IAAI;IAI3B,wBAAwB,CAAC,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI;IAWlE,qBAAqB,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI;IAYjE,IAAI;IAUJ,IAAI;IAYJ,kBAAkB,CAAC,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,IAAI,CAAC;IAE3C,kBAAkB;IAQlB,gBAAgB;IAMhB,yBAAyB,CAAC,UAAU,EAAE,MAAM;IAK5C,0BAA0B;IAMjC,SAAS,CAAC,WAAW,CAAC,CAAC,KAAK,EAAE,kBAAkB,GAAG,IAAI;IACvD,SAAS,CAAC,WAAW,CAAC,CAAC,KAAK,EAAE,kBAAkB,GAAG,IAAI;IACvD,SAAS,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,kBAAkB,GAAG,IAAI;IACrD,SAAS,CAAC,YAAY,CAAC,CAAC,KAAK,EAAE,kBAAkB,GAAG,IAAI;IACxD,SAAS,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,kBAAkB,GAAG,IAAI;IACrD,SAAS,CAAC,UAAU,CAAC,CAAC,KAAK,EAAE,kBAAkB,GAAG,IAAI;IACtD,SAAS,CAAC,MAAM,CAAC,IAAI,IAAI;IACzB,SAAS,CAAC,MAAM,CAAC,IAAI,IAAI;CAC5B;AAED,eAAe,cAAc,CAAC"}