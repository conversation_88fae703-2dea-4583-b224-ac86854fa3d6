import { _decorator, Component, Node, Vec3, input, Input, EventTouch, tween, Camera } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('DogController')
export class DogController extends Component {
    @property
    public speed: number = 300;
    
    @property(Camera)
    public camera: Camera = null;
    
    private targetPosition: Vec3 = new Vec3();
    private isMoving: boolean = false;

    start() {
        input.on(Input.EventType.TOUCH_START, this.onTouchStart, this);
        
        // 如果没有指定摄像机，自动获取主摄像机
        if (!this.camera) {
            this.camera = Camera.main;
        }
    }

    onTouchStart(event: EventTouch) {
        const touchPos = event.getUILocation();
        
        // 转换屏幕坐标到世界坐标（2D）
        this.camera.screenToWorld(new Vec3(touchPos.x, touchPos.y, 0), this.targetPosition);
        this.moveToTarget();
    }

    moveToTarget() {
        if (this.isMoving) return;
        
        this.isMoving = true;
        const distance = Vec3.distance(this.node.position, this.targetPosition);
        const duration = distance / this.speed;

        tween(this.node)
            .to(duration, { position: this.targetPosition })
            .call(() => {
                this.isMoving = false;
            })
            .start();
    }
}