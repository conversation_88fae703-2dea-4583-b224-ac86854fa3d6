{"version": 3, "file": "vec2.d.ts", "sourceRoot": "", "sources": ["../../../../source/script/utils/math/vec2.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,IAAI,CAAC;AAEhC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAGhE,UAAU,kBAAkB,CAAC,CAAC;IAC1B,MAAM,EAAE,MAAM,CAAC;IACf,CAAC,KAAK,EAAE,MAAM,GAAG,CAAC,CAAC;CACtB;AAQD,qBAAa,KAAK;IACd,OAAc,IAAI,iBAAiC;IACnD,OAAc,GAAG,iBAAiC;IAClD,OAAc,OAAO,iBAAmC;IACxD,OAAc,MAAM,iBAAiC;IACrD,OAAc,MAAM,iBAAiC;IAErD;;OAEG;WACW,KAAK,CAAC,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG;IAIjD;;OAEG;WACW,IAAI,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAM1D;;OAEG;WACW,GAAG,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM;IAMvE;;OAEG;WACW,GAAG,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAMjE;;OAEG;WACW,QAAQ,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAMtE;;OAEG;WACW,QAAQ,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAMtE;;OAEG;WACW,MAAM,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAMpE;;OAEG;WACW,IAAI,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAM1D;;OAEG;WACW,KAAK,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAM3D;;OAEG;WACW,GAAG,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAMjE;;OAEG;WACW,GAAG,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAMjE;;OAEG;WACW,KAAK,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAM3D;;OAEG;WACW,cAAc,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM;IAM/E;;OAEG;WACW,WAAW,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM;IAMxF;;OAEG;WACW,QAAQ,CAAC,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAM5D;;OAEG;WACW,eAAe,CAAC,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAMnE;;OAEG;WACW,GAAG,CAAC,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG;IAM/C;;OAEG;WACW,SAAS,CAAC,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG;IAMrD;;OAEG;WACW,MAAM,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAM5D;;OAEG;WACW,OAAO,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAM7D;;OAEG;WACW,WAAW,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAmBjE;;OAEG;WACW,SAAS,CAAC,GAAG,SAAS,SAAS,EAAE,QAAQ,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,QAAQ;IAYhG;;OAEG;WACW,GAAG,CAAC,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAIvD;;OAEG;WACW,KAAK,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAMpE;;OAEG;WACW,IAAI,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM;IAQ7E;;;OAGG;WACW,MAAM,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,EAAE,MAAM;IAQpE;;OAEG;WACW,aAAa,CAAC,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,SAAS;IAQ5G;;OAEG;WACW,aAAa,CAAC,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,SAAS;IAQ5G;;OAEG;WACW,GAAG,CAAC,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG;IAI/C;;;OAGG;WACW,OAAO,CAAC,GAAG,SAAS,kBAAkB,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,SAAI;IAM7F;;;OAGG;WACW,SAAS,CAAC,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,kBAAkB,CAAC,MAAM,CAAC,EAAE,GAAG,SAAI;IAMjG;;OAEG;WACW,YAAY,CAAC,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAIhE;;OAEG;WACW,MAAM,CAAC,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,OAAO,SAAU;IAO7E;;OAEG;WACW,KAAK,CAAC,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;CAY5D"}