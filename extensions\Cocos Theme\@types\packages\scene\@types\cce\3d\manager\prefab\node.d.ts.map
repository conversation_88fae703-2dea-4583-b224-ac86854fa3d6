{"version": 3, "file": "node.d.ts", "sourceRoot": "", "sources": ["../../../../../source/script/3d/manager/prefab/node.ts"], "names": [], "mappings": "AAAA,OAAO,EAA0C,SAAS,EAAwD,IAAI,EAAE,MAAM,EAAE,KAAK,EAAsB,MAAM,IAAI,CAAC;AAQtK,OAAO,EAAE,kBAAkB,EAAE,WAAW,EAAE,MAAM,+BAA+B,CAAC;AAMhF,aAAK,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;AAC3C,QAAA,MAAM,UAAU,iCAA2B,CAAC;AAG5C,aAAK,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;AAYnD,UAAU,iBAAiB;IACvB,QAAQ,EAAE,MAAM,EAAE,CAAC;IACnB,KAAK,EAAE,GAAG,CAAC;CACd;AAoBD,UAAU,eAAe;IACrB,UAAU,EAAE,UAAU,GAAG,IAAI,CAAC;CACjC;AAsCD,cAAM,aAAa;IACR,eAAe,EAAE,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAa;IACjD,yBAAyB,UAAS;IAEzC,OAAO,CAAC,UAAU,CAA8B;IAEzC,aAAa,CAAC,KAAK,EAAE,GAAG;IAsBxB,aAAa,CAAC,IAAI,EAAE,IAAI;IAe/B,OAAO,CAAC,mBAAmB;IA6DpB,0BAA0B,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,IAAI,GAAG,KAAK,GAAG,IAAI;IA8B1F,SAAS,CAAC,IAAI,EAAE,IAAI;IAWpB,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW;IAiChD;;;;;;OAMG;IACI,wBAAwB,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,iBAAiB,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI,GAAG,OAAO;IAyG3G,OAAO,CAAC,2BAA2B;IAwEnC,OAAO,CAAC,mBAAmB;IAQ3B,OAAO,CAAC,qBAAqB;IAI7B;;;;;;;OAOG;IACH,OAAO,CAAC,oBAAoB;IAqC5B,OAAO,CAAC,uBAAuB;IA4E/B;;;;OAIG;IACH,OAAO,CAAC,+BAA+B;IAqBvC,OAAO,CAAC,wBAAwB;IAiBhC;;;;OAIG;IACI,2BAA2B,CAAC,YAAY,EAAE,MAAM,CAAC,MAAM,CAAC,oBAAoB,EAAE,gBAAgB,EAAE,MAAM;IAY7G;;;;OAIG;IACI,+BAA+B,CAAC,cAAc,EAAE,cAAc,EAAE,gBAAgB,EAAE,MAAM;IAYxF,oBAAoB,CAAC,IAAI,EAAE,IAAI;IAoG/B,sBAAsB,CAAC,IAAI,EAAE,IAAI;IAqFjC,oBAAoB,CAAC,IAAI,EAAE,IAAI;IAiH/B,sBAAsB,CAAC,IAAI,EAAE,IAAI;cAmFxB,kBAAkB;IAKlC;;;OAGG;IACU,WAAW,CAAC,QAAQ,EAAE,MAAM;YAe3B,aAAa;YAgEb,eAAe;IAmHtB,kBAAkB,CAAC,IAAI,EAAE,IAAI;IA6GpC,OAAO,CAAC,2BAA2B;IAsD5B,sBAAsB,CAAC,IAAI,EAAE,IAAI,GAAG,OAAO;IA2BlD;;;OAGG;IACU,yBAAyB,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM;IA0DpE;;;;OAIG;IACU,uBAAuB,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI,EAAE,SAAS,EAAE,MAAM,GAAG,GAAG;IAgFrF;;;;OAIG;IACI,qBAAqB,CAAC,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI;IAmDtD,cAAc,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI;IAmF7D,+BAA+B,CAAC,IAAI,EAAE,IAAI;IAmB1C,8BAA8B,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,kBAAkB;IAyDnE,sBAAsB,CAAC,YAAY,EAAE,MAAM,CAAC,MAAM,CAAC,oBAAoB,EAAE,gBAAgB,EAAE,GAAG,EAAE,cAAc,EAAE,GAAG;IA+D1H;;;OAGG;IACU,YAAY,CAAC,QAAQ,EAAE,IAAI,GAAG,MAAM;IA2G1C,wBAAwB,CAAC,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC,EAAE,OAAO;IAkB3D,gCAAgC,CAAC,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC,EAAE,OAAO;IAiDnE,iCAAiC,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,CAAC,EAAE,OAAO;IA6C3F;;;;OAIG;IACI,oBAAoB,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI,EAAE,YAAY,CAAC,EAAE,OAAO;IA0BpE,gCAAgC,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI,EAAE,YAAY,CAAC,EAAE,OAAO;CA+D1F;AAED,QAAA,MAAM,aAAa,eAAsB,CAAC;AAE1C,OAAO,EAAE,aAAa,EAAE,eAAe,EAAE,CAAC"}