{"version": 3, "file": "gizmo-base.d.ts", "sourceRoot": "", "sources": ["../../../../../../source/script/public/gizmos/3d/elements/gizmo-base.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,IAAI,EAAE,MAAM,IAAI,CAAC;AAI1B,OAAO,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,MAAM,kCAAkC,CAAC;AAK3F,cAAM,SAAS;IACX,SAAS,CAAC,SAAS,UAAS;IAC5B,SAAS,CAAC,UAAU,UAAS;IAC7B,SAAS,CAAC,QAAQ,UAAS;IAC3B,SAAS,CAAC,SAAS,UAAS;IAC5B,SAAS,CAAC,OAAO,UAAQ;IACzB,SAAS,CAAC,OAAO,EAAE,GAAG,CAAQ;IAC9B,SAAS,CAAC,SAAS,EAAE,GAAG,CAAQ;IAChC,SAAS,CAAC,eAAe,UAAS;IAClC,SAAS,CAAC,SAAS,UAAS;IAC5B,SAAS,CAAC,cAAc,CAAC,IAAI,IAAI;IACjC,SAAS,CAAC,IAAI,CAAC,IAAI,IAAI;IACvB,SAAS,CAAC,MAAM,CAAC,IAAI,IAAI;IACzB,SAAS,CAAC,MAAM,CAAC,IAAI,IAAI;IAClB,QAAQ,CAAC,CAAC,SAAS,EAAE,MAAM,GAAG,IAAI;IAClC,SAAS,CAAC,IAAI,IAAI;IAClB,aAAa,CAAC,CAAC,KAAK,EAAE,kBAAkB,GAAG,IAAI;IAC/C,SAAS,CAAC,CAAC,KAAK,EAAE,mBAAmB,GAAG,IAAI;IAC5C,OAAO,CAAC,CAAC,KAAK,EAAE,mBAAmB,GAAG,IAAI;IAC1C,0BAA0B,CAAC,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAC/C,iCAAiC,UAAS;IAEjD,YAAY,CAAC,IAAI,EAAE,IAAI;gBAIX,MAAM,EAAE,GAAG;IAIvB,IAAI,MAAM,QAET;IAED,IAAI,MAAM,IAAI,OAAO,CAEpB;IAGD,IAAI,MAAM,CAAC,KAAK,KAAA,EAYf;IAGD,KAAK;IAIL,YAAY;IAQZ,cAAc,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI;IAStC,eAAe,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI;IAMvC,YAAY,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI;IASpC,aAAa;IAQb,aAAa;IAWb,gBAAgB;IAIhB,WAAW;IASX,OAAO;IAKP,IAAI;IAYJ,OAAO;IASP,IAAI;IAmBJ,MAAM,CAAC,SAAS,EAAE,MAAM;IAMxB,IAAI,IAAI,QAaP;IAED,IAAI,KAAK,UAqBR;IAED,IAAI,QAAQ,IAAI,IAAI,EAAE,CAgBrB;IAED,IAAI,SAAS,YAEZ;IAED,IAAI,SAAS,CAAC,KAAK,SAAA,EAElB;IAED,IAAI,OAAO,YAEV;IAED,IAAI,OAAO,CAAC,KAAK,SAAA,EAEhB;IAED,IAAI,QAAQ,YAEX;IAED,IAAI,QAAQ,CAAC,KAAK,SAAA,EAEjB;IAED,eAAe,CAAC,QAAQ,EAAE,MAAM;IAWhC,SAAS,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI;IAIhC,mBAAmB;IAEnB,wBAAwB;IAIxB,yBAAyB;CAGnC;AAED,eAAe,SAAS,CAAC"}