{"version": 3, "file": "mat4.d.ts", "sourceRoot": "", "sources": ["../../../../source/script/utils/math/mat4.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAChE,OAAO,EAAE,IAAI,EAAc,MAAM,IAAI,CAAC;AAMtC,UAAU,kBAAkB,CAAC,CAAC;IAC1B,MAAM,EAAE,MAAM,CAAC;IACf,CAAC,KAAK,EAAE,MAAM,GAAG,CAAC,CAAC;CACtB;AAUD,qBAAa,KAAK;IACd,OAAc,QAAQ,iBAA6B;IAEnD;;OAEG;WACW,KAAK,CAAE,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG;IASlD;;OAEG;WACW,IAAI,CAAE,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAoB3D;;OAEG;WACW,GAAG,CAAE,GAAG,SAAS,SAAS,EACpC,GAAG,EAAE,GAAG,EACR,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAClD,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAClD,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAClD,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM;IAStD;;OAEG;WACW,QAAQ,CAAE,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG;IAoBvD;;OAEG;WACW,SAAS,CAAE,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAqChE;;OAEG;WACW,MAAM,CAAE,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IA8C7D;;OAEG;WACW,WAAW,CAAE,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG,GAAG,MAAM;IAuBjE;;OAEG;WACW,QAAQ,CAAE,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAiCvE;;OAEG;WACW,SAAS,CAAE,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,OAAO;IAyBvG;;OAEG;WACW,SAAS,CAAE,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,OAAO;IAiBvG;;OAEG;WACW,KAAK,CAAE,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,OAAO;IAqBnG;;;;OAIG;WACW,MAAM,CAAE,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO;IAoDpH;;;OAGG;WACW,OAAO,CAAE,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM;IAoC3E;;;OAGG;WACW,OAAO,CAAE,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM;IAoC3E;;;OAGG;WACW,OAAO,CAAE,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM;IAqC3E;;OAEG;WACW,eAAe,CAAE,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,OAAO;IAoBrG;;OAEG;WACW,WAAW,CAAE,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,OAAO;IAoBjG;;OAEG;WACW,YAAY,CAAE,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO;IAqClH;;OAEG;WACW,aAAa,CAAE,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM;IAuBzE;;OAEG;WACW,aAAa,CAAE,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM;IAuBzE;;OAEG;WACW,aAAa,CAAE,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM;IAuBzE;;OAEG;WACW,MAAM,CAAE,GAAG,SAAS,SAAS,EAAE,QAAQ,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,OAAO;IAoCrI;;OAEG;WACW,cAAc,CAAE,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG;IAQtG;;OAEG;WACW,UAAU,CAAE,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG;IAkBlG;;OAEG;WACW,WAAW,CAAE,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG;IAiC1E;;OAEG;WACW,KAAK,CAAE,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO;IAmBnH;;OAEG;WACW,OAAO,CAAE,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO;IAuCvH;;;;;;OAMG;WACW,aAAa,CAAE,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO;IA4CzI;;OAEG;WACW,QAAQ,CAAE,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,SAAS;IAuCrE;;;;;;;;OAQG;WACW,OAAO,CAAE,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM;IAwB3I;;;;;;OAMG;WACW,WAAW,CAAE,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM;IAuBnH;;;;;;;;OAQG;WACW,KAAK,CAAE,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM;IAuBzI;;;;;OAKG;WACW,MAAM,CAAE,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,OAAO;IAoD5H;;OAEG;WACW,gBAAgB,CAAE,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAmDvE;;;OAGG;WACW,OAAO,CAAE,GAAG,SAAS,kBAAkB,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,SAAI;IAoB9F;;;OAGG;WACW,SAAS,CAAE,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,kBAAkB,CAAC,MAAM,CAAC,EAAE,GAAG,SAAI;IAoBlG;;OAEG;WACW,GAAG,CAAE,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAoBlE;;OAEG;WACW,QAAQ,CAAE,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAoBvE;;OAEG;WACW,cAAc,CAAE,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM;IAoBhF;;OAEG;WACW,oBAAoB,CAAE,GAAG,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM;IAoBlG;;OAEG;WACW,YAAY,CAAE,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;IAOjE;;OAEG;WACW,MAAM,CAAE,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,OAAO,SAAU;CAsBjF"}