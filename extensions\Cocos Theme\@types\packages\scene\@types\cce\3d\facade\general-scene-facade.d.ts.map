{"version": 3, "file": "general-scene-facade.d.ts", "sourceRoot": "", "sources": ["../../../../source/script/3d/facade/general-scene-facade.ts"], "names": [], "mappings": ";;;;AAEA,OAAO,EACH,sBAAsB,EACtB,iBAAiB,EACjB,cAAc,EACd,6BAA6B,EAC7B,+BAA+B,EAC/B,cAAc,EACd,gBAAgB,EAChB,gBAAgB,EAChB,mBAAmB,EACnB,kBAAkB,EAClB,sBAAsB,EACtB,iBAAiB,EACjB,kBAAkB,EACrB,MAAM,2BAA2B,CAAC;AAEnC,OAAO,EAAE,cAAc,EAAE,UAAU,EAAE,kBAAkB,EAAE,WAAW,EAAE,MAAM,4BAA4B,CAAC;AACzG,OAAO,EAAE,iBAAiB,EAAE,aAAa,EAAE,MAAM,gCAAgC,CAAC;AAclF,OAAO,UAAU,MAAM,oCAAoC,CAAC;AAI5D,OAAO,EAAE,SAAS,EAAY,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAQ,MAAM,IAAI,CAAC;AAC5E,OAAO,eAAe,MAAM,sCAAsC,CAAC;AAEnE,OAAO,EAAE,YAAY,EAAE,MAAM,mCAAmC,CAAC;AASjE,OAAO,0BAA0B,MAAM,yEAAyE,CAAC;AACjH,OAAO,EAAE,+BAA+B,EAAE,0BAA0B,EAAE,6BAA6B,EAAE,MAAM,+CAA+C,CAAC;AAC3J,OAAO,EAAE,UAAU,EAAE,MAAM,uCAAuC,CAAC;AAGnE,qBAAa,kBAAmB,YAAW,iBAAiB,EAAE,0BAA0B;IACpF,SAAS,CAAC,SAAS,mDAAY;IAC/B,SAAS,CAAC,UAAU,qCAAa;IACjC,SAAS,CAAC,QAAQ,wCAAW;IAC7B,SAAS,CAAC,QAAQ,6CAAW;IAC7B,SAAS,CAAC,SAAS,0CAAY;IAC/B,SAAS,CAAC,SAAS,8DAAY;IAC/B,SAAS,CAAC,mBAAmB,wDAA8B;IAC3D,SAAS,CAAC,eAAe,gDAA0B;IACnD,SAAS,CAAC,UAAU,6CAAa;IACjC,SAAS,CAAC,WAAW,8CAAc;IACnC,SAAS,CAAC,aAAa,kDAAgB;IACvC,SAAS,CAAC,aAAa,gDAAgB;IACvC,SAAS,CAAC,UAAU,6CAAa;IACjC,SAAS,CAAC,WAAW,8CAAc;IACnC,SAAS,CAAC,UAAU,4CAAa;IACjC,SAAS,CAAC,QAAQ,EAAE,eAAe,CAAgB;IAEnD,SAAS,CAAC,UAAU,4CAAa;IACjC,SAAS,CAAC,UAAU,4CAAa;IAEjC,SAAS,CAAC,MAAM,oCAAS;IACzB,SAAS,CAAC,YAAY,gDAAe;IACrC,SAAS,CAAC,OAAO,sCAAU;IAC3B,SAAS,CAAC,gBAAgB,kDAA2B;IACrD,SAAS,CAAC,eAAe,gDAA0B;IACnD,SAAS,CAAC,aAAa,mDAAgB;IACvC,SAAS,CAAC,cAAc,qDAAiB;IACzC,SAAS,CAAC,WAAW,EAAG,UAAU,CAAC;IAC5B,SAAS,CAAC,EAAE,iBAAiB,CAAC;IAG9B,MAAM,UAAS;IACf,QAAQ,EAAE,aAAa,CAAyB;IAChD,kBAAkB,UAAS;IAElC,OAAO,CAAC,kBAAkB,CAAM;IAChC,SAAS,CAAC,mBAAmB,EAAE,YAAY,EAAE,CAAM;IAInD,OAAO,CAAC,2BAA2B,CAAoC;IACvE,OAAO,CAAC,mBAAmB,CAAS;IAEpC,IAAW,kBAAkB,CAAC,KAAK,EAAE,OAAO,EAO3C;IAED,IAAW,kBAAkB,IAAI,OAAO,CAEvC;IAED,OAAO,CAAC,8BAA8B,CAAS;IAC/C,IAAW,6BAA6B,CAAC,KAAK,EAAE,OAAO,EAOtD;IAED,IAAW,6BAA6B,IAAI,OAAO,CAElD;IAEM,IAAI;IAOJ,iBAAiB;IAoBjB,kCAAkC,CAAC,QAAQ,EAAE,0BAA0B;IAIjE,KAAK,CAAC,IAAI,EAAE,GAAG;IAe5B,SAAS,CAAC,aAAa;IAWV,IAAI;IAKV,qBAAqB;IAIf,YAAY,IAAI,OAAO,CAAC,OAAO,CAAC;IAIhC,eAAe,IAAI,OAAO,CAAC,OAAO,CAAC;IAQnC,iBAAiB,IAAI,OAAO,CAAC,OAAO,CAAC;IAK3C,cAAc;IAIR,SAAS,CAAC,IAAI,EAAE,MAAM;IAkCtB,UAAU;IASV,SAAS,CAAC,KAAK,EAAE,OAAO;IAK9B,cAAc;IAGR,eAAe;IAIf,iBAAiB,CAAC,IAAI,CAAC,EAAE,GAAG;IAI5B,eAAe,CAAC,IAAI,CAAC,EAAE,GAAG;IAM1B,WAAW;IAIX,aAAa,CAAC,IAAI,EAAE,MAAM;IAI1B,qBAAqB,CAAC,IAAI,EAAE,MAAM;IAIlC,wBAAwB,IAAI,OAAO,CAAC,MAAM,CAAC;IAI3C,eAAe;IAIf,YAAY,CAAC,OAAO,CAAC,EAAE,mBAAmB;;;IAI1C,eAAe;;;;;;IAIf,uBAAuB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAIvD,iBAAiB;;;;IAIjB,wBAAwB;IAIrC;;OAEG;IACI,SAAS;IAIT,qBAAqB;IAIrB,cAAc,CAAC,SAAS,EAAE,MAAM,YAAY,EAAE,GAAG,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC;IAS/D,aAAa,CAAC,KAAK,EAAE,GAAG;IAKxB,aAAa,CAAC,KAAK,EAAE,GAAG;IAIxB,aAAa,CAAC,KAAK,EAAE,GAAG;IAIxB,QAAQ,CAAC,IAAI,EAAE,SAAS;IAOlB,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAIzC,4BAA4B,CAAC,IAAI,EAAE,MAAM;IAIzC,sBAAsB,CAAC,OAAO,EAAE,kBAAkB,GAAG,OAAO,CAAC,OAAO,CAAC;IAIrE,4BAA4B,CAAC,OAAO,EAAE,kBAAkB,GAAG,OAAO,CAAC,OAAO,CAAC;IAI3E,eAAe,CAAC,OAAO,EAAE,kBAAkB,GAAG,OAAO,CAAC,OAAO,CAAC;IAI9D,SAAS,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAIzC,iBAAiB,CAAC,OAAO,EAAE,kBAAkB,GAAG,OAAO,CAAC,OAAO,CAAC;IAIhE,0BAA0B,CAAC,OAAO,EAAE,kBAAkB,GAAG,OAAO,CAAC,OAAO,CAAC;IAIzE,uBAAuB,CAAC,OAAO,EAAE,kBAAkB;IAInD,oBAAoB,CAAC,OAAO,EAAE,gBAAgB;IAU9C,sBAAsB,CAAC,OAAO,EAAE,kBAAkB;IAIlD,yBAAyB,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAInF,cAAc,IAAI,IAAI;IAQtB,QAAQ,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE;IAI5C,aAAa,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE;IAI3C,SAAS,CAAC,OAAO,EAAE,gBAAgB,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;IAavD,aAAa,CAAC,OAAO,EAAE,cAAc,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;IAQzD,UAAU,CAAC,OAAO,EAAE,iBAAiB,GAAG,OAAO,CAAC,GAAG,CAAC;IAYpD,UAAU,CAAC,OAAO,EAAE,iBAAiB;IAKrC,cAAc,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO;IAIvE,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAItE,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,GAAE,kBAAuB;IAUvD,SAAS,CAAC,IAAI,EAAE,IAAI;IASpB,YAAY,CAAC,IAAI,EAAE,IAAI;IAavB,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,GAAE,WAAgB;IAS9C,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW;IAgBrC,cAAc,CAAC,IAAI,EAAE,MAAM;IAIjC,eAAe,CAAC,OAAO,EAAE,sBAAsB;IAIzC,cAAc,CAAC,IAAI,EAAE,MAAM;IAI3B,eAAe,CAAC,OAAO,EAAE,sBAAsB;IAI/C,sBAAsB,CAAC,OAAO,EAAE,6BAA6B,GAAG,OAAO,CAAC,OAAO,CAAC;IAIhF,wBAAwB,CAAC,OAAO,EAAE,+BAA+B;IAIvE,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,GAAE,WAAgB;IAKtD,iBAAiB,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,GAAE,WAAgB;IAMzD,gBAAgB,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,GAAE,WAAgB;IAKxD,kBAAkB,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,GAAE,WAAgB;IAS1D,QAAQ,CAAC,OAAO,CAAC,EAAE,GAAG;IAItB,aAAa;IAIP,IAAI;IAIJ,IAAI;IAIV,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,UAAO;IAU9B,eAAe,IAAI,OAAO,CAAC,GAAG,CAAC;IAI/B,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAIzC,WAAW,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAI7C,mBAAmB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAI/C,oBAAoB,CAAC,IAAI,EAAE,GAAG;IAI9B,mBAAmB,CAAC,IAAI,EAAE,MAAM;IAIhC,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE;QAAE,IAAI,CAAC,EAAE,OAAO,CAAA;KAAE;IAItE,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG;IAI7C,qBAAqB,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;IAI9C,oBAAoB,CAAC,IAAI,EAAE,MAAM;IAIjC,2BAA2B,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;IAIpD,0BAA0B,CAAC,IAAI,EAAE,MAAM;IAIvC,mBAAmB,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;IAI5C,kBAAkB,CAAC,IAAI,EAAE,MAAM;IAI/B,kBAAkB,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG;IAIpD,oBAAoB,CAAC,IAAI,EAAE,MAAM;IAIjC,0BAA0B,CAAC,IAAI,EAAE,MAAM;IAIvC,kBAAkB,CAAC,IAAI,EAAE,MAAM;IAI/B,wBAAwB;IAIlB,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU;IAIlE,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;IAInC,YAAY,CAAC,IAAI,EAAE,MAAM;IAQnB,kBAAkB,IAAI,OAAO,CAAC,IAAI,CAAC;IAInC,kBAAkB,IAAI,OAAO,CAAC,MAAM,CAAC;IAIrC,eAAe,IAAI,OAAO,CAAC,MAAM,CAAC;IAIlC,oBAAoB,IAAI,OAAO,CAAC,MAAM,CAAC;IAIvC,SAAS,IAAI,OAAO,CAAC,OAAO,CAAC;IAInC,kBAAkB,IAAI,OAAO;IAI7B,kBAAkB,IAAI,MAAM;IAI5B,sBAAsB;IAIhB,oBAAoB,CAAC,IAAI,EAAE,6BAA6B;IAQxD,QAAQ,CAAC,IAAI,EAAE,0BAA0B;IAIzC,aAAa,CAAC,IAAI,EAAE,+BAA+B;IAInD,OAAO,CAAC,KAAK,EAAE,OAAO;IAItB,cAAc,CAAC,IAAI,EAAE,OAAO;IAI5B,gBAAgB,CAAC,IAAI,EAAE,MAAM;IAOnC,KAAK,CAAC,IAAI,GAAE,MAAM,EAAE,GAAG,IAAW,EAAE,QAAQ,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,EAAE,IAAI,EAAE,UAAU,CAAC,EAAE,IAAI,EAAE,SAAS,UAAQ;IAI1G,oBAAoB,CAAC,KAAK,EAAE,MAAM,EAAE;IAIpC,kBAAkB;IAIlB,cAAc,CAAC,OAAO,EAAE,OAAO;IAI/B,aAAa;IAOb,iBAAiB;IAOjB,gBAAgB,CAAC,KAAK,EAAE,MAAM,EAAE;IAIhC,iBAAiB,IAAI,GAAG;IAGxB,iBAAiB,CAAC,IAAI,EAAE,GAAG;IAI3B,mBAAmB,IAAI,MAAM;IAG7B,mBAAmB,CAAC,KAAK,EAAE,MAAM;IAIjC,oBAAoB,IAAI,MAAM;IAG9B,oBAAoB,CAAC,KAAK,EAAE,MAAM;IAIlC,eAAe,IAAI,IAAI;IAGvB,iBAAiB,IAAI,IAAI;IAGzB,kBAAkB,IAAI,IAAI;IAO1B,0BAA0B,IAAI,GAAG;IAIjC,yBAAyB,IAAI,GAAG;IAGhC,sBAAsB,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM;IAG5C,sBAAsB,CAAC,IAAI,EAAE,MAAM,GAAG,GAAG;IAGzC,sBAAsB,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,GAAG;IAG/D,wBAAwB,CAAC,IAAI,EAAE,MAAM,GAAG,GAAG;IAG3C,uBAAuB,CAAC,QAAQ,EAAE,MAAM,GAAG,GAAG;IAG9C,6BAA6B,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM;IAGvD,8BAA8B,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,GAAG;IAIjG,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAKnF,uBAAuB,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAGzE,cAAc,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAI9C,eAAe,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAIpE,WAAW,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAG/C,QAAQ,IAAI,OAAO,CAAC,OAAO,CAAC;IAI5B,uBAAuB,CAAC,aAAa,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC,cAAc,CAAC;IAOvF,0BAA0B,CAAC,IAAI,EAAE,MAAM;IAOjC,eAAe,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAI3C,cAAc,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAI1C,UAAU,CAAC,IAAI,EAAE,MAAM;IAIvB,YAAY,CAAC,IAAI,EAAE,GAAG;IAItB,YAAY,CAAC,IAAI,EAAE,GAAG;IAO5B,WAAW,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAG/B,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAGjC,cAAc,IAAI,MAAM,EAAE;IAG1B,YAAY,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO;IAGnC,UAAU,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAG9B,YAAY,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAGhC,cAAc,IAAI,IAAI;IAOtB,eAAe,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI;IAItC,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI;IAIpC,YAAY,CAAC,IAAI,EAAE,MAAM;IAOzB,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,GAAG,IAAI;IAO9C,YAAY,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,GAAG,GAAG;IAG5C,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,GAAG;IAGhC,UAAU,CAAC,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,GAAG;IAGpD,YAAY,CAAC,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,OAAO,GAAG,GAAG;IAGpD,WAAW,CAAC,QAAQ,EAAE,MAAM;IAOzC,qBAAqB,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAIzC,gBAAgB,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAMpC,qBAAqB,CAAC,IAAI,EAAE,MAAM;;;;;;IAGlC,oBAAoB,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM;IAGhD;;;OAGG;IACI,YAAY;IAGnB;;;OAGG;IACI,eAAe;IAGtB;;;OAGG;IACI,aAAa;IAGpB;;;OAGG;IACI,YAAY;IAQnB,sBAAsB,CAAC,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC;IAG7C,SAAS,CAAC,YAAY,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,UAAU,CAAC,OAAO,IAAI,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI;IAG9F,QAAQ,CAAC,YAAY,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,UAAU,CAAC,OAAO,IAAI,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI;IAmBtF,kBAAkB;IA6CjB,cAAc;IASd,yBAAyB,CAAC,IAAI,EAAE,MAAM;IAOhC,mBAAmB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAO5D,8BAA8B;IAI9B,2BAA2B;IAM3B,wBAAwB,CAAC,IAAI,EAAE,OAAO,GAAG,SAAS;IAKlD,uBAAuB,IAAI,OAAO;IAIlC,mCAAmC,CAAC,IAAI,EAAE,OAAO,GAAG,SAAS;IAK7D,kCAAkC,IAAI,OAAO;IAI7C,qBAAqB;IAOrB;;;;OAIG;IACH,yBAAyB,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI;IAK9C,0BAA0B,CAAC,IAAI,EAAE,OAAO;CAK3C;AAED,eAAe,kBAAkB,CAAC"}