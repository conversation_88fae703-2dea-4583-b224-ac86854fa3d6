{"version": 3, "file": "utils.d.ts", "sourceRoot": "", "sources": ["../../../../../source/script/3d/manager/animation/utils.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,aAAa,EAAE,gBAAgB,EAAE,SAAS,EAAE,MAAM,+BAA+B,CAAC;AAEpH,OAAO,EACH,SAAS,EAET,aAAa,EAGb,SAAS,EAIT,IAAI,EAOJ,cAAc,EAEjB,MAAM,IAAI,CAAC;AAEZ,OAAO,EAEH,SAAS,EACT,UAAU,EACV,eAAe,EACf,SAAS,EACT,aAAa,EAEb,SAAS,EAET,UAAU,EACb,MAAM,gBAAgB,CAAC;AAQxB,OAAO,EAAgH,gBAAgB,EAAyC,MAAM,2BAA2B,CAAC;AAKlN,QAAA,MAAM,oBAAoB,UAKzB,CAAC;AAuEF,iBAAS,eAAe,CAAC,IAAI,EAAE,GAAG,WAEjC;AA8BD,cAAM,aAAa;IACR,YAAY,CAAC,UAAU,EAAE,GAAG;IAQ5B,uBAAuB,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;IA2BlD,qBAAqB,CAAC,QAAQ,EAAE,GAAG;IAanC,eAAe,CAAC,KAAK,EAAE,CAAC,aAAa,GAAG,IAAI,CAAC,EAAE;IAkBtD;;;;;;OAMG;IACI,eAAe,CAAC,eAAe,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,SAAS,CAAC,kBAAkB,GAAG,SAAS;IA+C/H,gBAAgB,CAAC,YAAY,EAAE,SAAS,EAAE;IAuBpC,uBAAuB,CAAC,QAAQ,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;IA4B7E,cAAc,CAAC,IAAI,CAAC,EAAE,MAAM;IAY5B,mBAAmB,CAAC,SAAS,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,SAAS,EAAE;IAwBtG,WAAW,CAAC,WAAW,EAAE,SAAS,CAAC,SAAS,EAAE,YAAY,CAAC,EAAE,GAAG;IAqBhE,qBAAqB,CAAC,QAAQ,EAAE,MAAM;IAS7C;;;;OAIG;IACI,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAE,IAAI;;;;;;;;;;IA6F3D,mBAAmB,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,CAAC,KAAK,GAAG,UAAU;IAmCnE,cAAc,CAAC,KAAK,EAAE,SAAS,CAAC,KAAK,GAAG,SAAS;IAuCjD,QAAQ,CAAC,OAAO,EAAE,aAAa,EAAE,OAAO,EAAE,aAAa;IAqB9D;;;OAGG;IACI,WAAW,CAAC,IAAI,EAAE,aAAa;IAQ/B,QAAQ,CAAC,UAAU,EAAE,GAAG;IAQ/B,OAAO,CAAC,aAAa;IAMrB;;;;;;;;OAQG;IACI,kBAAkB,CACrB,YAAY,EAAE,MAAM,EACpB,QAAQ,EAAE,MAAM,EAChB,QAAQ,EAAE,MAAM,EAChB,UAAU,EAAE,GAAG,EACf,aAAa,EAAE,SAAS,CAAC,SAAS,EAClC,WAAW,EAAE,MAAM,EAAE;IAiClB,oBAAoB,CAAC,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,SAAS,CAAC,SAAS;IAqC/F,mBAAmB,CAAC,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,SAAS,CAAC,SAAS;IAkBhH,gBAAgB,CACnB,YAAY,EAAE,MAAM,EACpB,QAAQ,EAAE,MAAM,EAChB,QAAQ,EAAE,MAAM,EAChB,UAAU,EAAE,GAAG,EACf,aAAa,EAAE,SAAS,CAAC,SAAS,EAClC,WAAW,EAAE,MAAM,EAAE;IA0ElB,eAAe,CAClB,YAAY,EAAE,MAAM,EACpB,QAAQ,EAAE,MAAM,EAChB,QAAQ,EAAE,MAAM,EAChB,UAAU,EAAE,GAAG,EACf,aAAa,EAAE,SAAS,CAAC,SAAS,EAClC,WAAW,EAAE,MAAM,EAAE;IAYlB,0BAA0B,CAAC,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM;IAsD3D;;;;OAIG;IACI,4BAA4B,CAAC,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM;IA8BrE;;;OAGG;IACI,6BAA6B,CAAC,SAAS,EAAE,SAAS;IAwBzD;;;;OAIG;IACI,qBAAqB,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO;IA4ClD,WAAW,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,aAAa,GAAG,IAAI,CAAC,EAAE;IAgBpE;;;;OAIG;IACI,sBAAsB,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,EAAE,QAAQ,CAAC,EAAE,MAAM;IAiE9D,eAAe,CAAC,IAAI,CAAC,EAAE,MAAM;IAwBpC;;;;;;OAMG;IACU,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,GAAE,eAAe,GAAG,IAAW;IAsEpG;;;;;OAKG;IACI,gBAAgB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,KAAK,IAAI;IAWpF,aAAa,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU;IActD,QAAQ,CAAC,GAAG,EAAE,GAAG;IAIjB,cAAc,CAAC,QAAQ,EAAE,MAAM;IAyBtC;;;;OAIG;IACI,kBAAkB,CAAC,IAAI,EAAE,MAAM,GAAG,SAAS;IAQ3C,qBAAqB,CAAC,kBAAkB,EAAE,gBAAgB;IAI1D,aAAa,CAAC,KAAK,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI;IAUtD,eAAe,CAAC,QAAQ,EAAE,gBAAgB,GAAG,SAAS,GAAG,aAAa,GAAG,IAAI;CAiBhF;AAED,QAAA,MAAM,KAAK,eAAsB,CAAC;AAClC,OAAO,EAAE,KAAK,EAAE,eAAe,EAAE,oBAAoB,EAAE,CAAC"}