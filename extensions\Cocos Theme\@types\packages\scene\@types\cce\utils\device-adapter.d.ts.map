{"version": 3, "file": "device-adapter.d.ts", "sourceRoot": "", "sources": ["../../../source/script/utils/device-adapter.ts"], "names": [], "mappings": "AAMA,cAAM,aAAa;IAEf,OAAO,CAAC,YAAY,CAAQ;IAC5B,OAAO,CAAC,aAAa,CAAQ;IAE7B,OAAO,CAAC,MAAM,CAAK;IACnB,OAAO,CAAC,OAAO,CAAS;IAExB,OAAO,CAAC,YAAY,CAAK;IACzB,OAAO,CAAC,aAAa,CAAK;IAE1B,IAAI,WAAW,IAGI,MAAM,CADxB;IACD,IAAI,WAAW,CAAC,CAAC,EAAE,MAAM,EAExB;IAED,IAAI,YAAY,IAGI,MAAM,CADzB;IACD,IAAI,YAAY,CAAC,CAAC,EAAE,MAAM,EAEzB;IAED,IAAI,KAAK,IAGI,MAAM,CADlB;IACD,IAAI,KAAK,CAAC,CAAC,EAAE,MAAM,EAElB;IAED,IAAI,MAAM,IAGI,MAAM,CADnB;IACD,IAAI,MAAM,CAAC,CAAC,EAAE,MAAM,EAEnB;IAED,IAAI,KAAK,WAER;IAED,IAAI,MAAM,IAIS,OAAO,CAFzB;IAED,IAAI,MAAM,CAAC,MAAM,EAAE,OAAO,EAEzB;IAED,OAAO,CAAC,WAAW;IAcnB;;;;;;;;OAQG;IACI,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,UAAQ,EAAE,MAAM,UAAQ,EAAE,KAAK,SAAI;IAmC/E,WAAW,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM;;;;IAmBhC,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;IAQ1C,IAAW,MAAM,WAEhB;IACD,IAAW,MAAM,WAEhB;CACJ;AAED,QAAA,MAAM,UAAU,eAAsB,CAAC;AACvC,QAAA,MAAM,aAAa,eAAsB,CAAC;AAM1C,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,CAAC"}