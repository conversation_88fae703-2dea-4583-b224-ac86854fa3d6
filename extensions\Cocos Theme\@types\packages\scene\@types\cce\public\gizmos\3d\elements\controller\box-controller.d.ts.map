{"version": 3, "file": "box-controller.d.ts", "sourceRoot": "", "sources": ["../../../../../../../source/script/public/gizmos/3d/elements/controller/box-controller.ts"], "names": [], "mappings": "AAEA,OAAO,kBAAkB,MAAM,uBAAuB,CAAC;AAIvD,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAA8B,MAAM,IAAI,CAAC;AACnE,OAAO,EAAE,kBAAkB,EAAE,MAAM,kBAAkB,CAAC;AAkBtD,cAAM,aAAc,SAAQ,kBAAkB;IAC1C,OAAO,CAAC,OAAO,CAAoB;IACnC,OAAO,CAAC,KAAK,CAA2B;IACxC,OAAO,CAAC,UAAU,CAAoB;IACtC,OAAO,CAAC,iBAAiB,CAAqB;IAC9C,OAAO,CAAC,yBAAyB,CAA6B;IAC9D,OAAO,CAAC,SAAS,CAAqB;IACtC,OAAO,CAAC,WAAW,CAA6B;IAChD,OAAO,CAAC,cAAc,CAAoB;IAC1C,OAAO,CAAC,cAAc,CAAK;gBAEf,QAAQ,EAAE,IAAI;IAQ1B,QAAQ,CAAC,KAAK,EAAE,KAAK;IAQrB,UAAU,CAAC,OAAO,EAAE,MAAM;IAM1B,iBAAiB,CAAC,QAAQ,EAAE,MAAM;IAgBlC,SAAS;IAwBT,UAAU,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI;IAiB7C,WAAW,CAAC,KAAK,EAAE,kBAAkB;IAWrC,WAAW,CAAC,KAAK,EAAE,kBAAkB;IAuBrC,SAAS,CAAC,KAAK,EAAE,kBAAkB;IAOnC,YAAY,CAAC,KAAK,EAAE,kBAAkB;IAItC,SAAS,CAAC,KAAK,EAAE,kBAAkB;IAInC,UAAU;IAIV,YAAY;IAGZ,eAAe;IAIf,eAAe;CAKlB;AAED,eAAe,aAAa,CAAC"}