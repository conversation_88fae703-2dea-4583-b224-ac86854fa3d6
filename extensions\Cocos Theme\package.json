{"package_version": 2, "version": "1.2.3", "name": "cocos-theme", "description": "i18n:cocos-theme.description", "main": "./dist/main.js", "dependencies": {"axios": "^1.6.2", "fs-extra": "^10.0.0", "vue": "^3.1.4"}, "devDependencies": {"@types/fs-extra": "^9.0.5", "@types/node": "^16.0.1", "typescript": "^4.3.4"}, "panels": {"default": {"title": "i18n:cocos-theme.title", "type": "dockable", "main": "dist/panels/default", "icon": "./static/images/icon.png", "size": {"min-width": 300, "min-height": 600, "width": 430, "height": 680}}}, "contributions": {"menu": [{"path": "i18n:menu.extension", "label": "i18n:cocos-theme.title", "message": "open-panel"}], "messages": {"open-panel": {"methods": ["openPanel"]}}}, "author": "<PERSON>", "editor": ">=3.6.0", "scripts": {"build": "tsc -b", "watch": "tsc -w"}, "_storeId": "30e327dff2099312bc1506246c40dc4c"}