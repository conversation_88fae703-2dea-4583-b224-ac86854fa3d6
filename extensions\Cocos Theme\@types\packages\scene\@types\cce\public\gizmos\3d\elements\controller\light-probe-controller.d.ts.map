{"version": 3, "file": "light-probe-controller.d.ts", "sourceRoot": "", "sources": ["../../../../../../../source/script/public/gizmos/3d/elements/controller/light-probe-controller.ts"], "names": [], "mappings": ";AAAA,OAAO,cAAc,MAAM,mBAAmB,CAAC;AAE/C,OAAO,EACH,KAAK,EAGL,eAAe,EAKf,IAAI,EAIJ,IAAI,EAGP,MAAM,IAAI,CAAC;AACZ,OAAO,aAAa,MAAM,6BAA6B,CAAC;AACxD,OAAO,EAAwB,kBAAkB,EAAE,MAAM,kBAAkB,CAAC;AAC5E,OAAO,kBAAkB,MAAM,kCAAkC,CAAC;AAClE,OAAO,0BAA0B,MAAM,4CAA4C,CAAC;AACpF,OAAO,2BAA2B,MAAM,4CAA4C,CAAC;AACrF,OAAO,EAAE,gBAAgB,EAAE,MAAM,qCAAqC,CAAC;AAEvE,OAAO,SAAoB,MAAM,yBAAyB,CAAC;AAI3D,OAAO,KAAK,eAAe,MAAM,uCAAuC,CAAC;AAMzE,oBAAY,SAAS,GAAG;IAAE,IAAI,EAAE,IAAI,CAAC;IAAC,KAAK,EAAE,IAAI,CAAA;CAAE,CAAA;AAInD,MAAM,CAAC,OAAO,OAAO,oBAAqB,SAAQ,cAAe,YAAW,0BAA0B,EAAE,2BAA2B;IA2DpH,cAAc,EAAE,cAAc,CAAC,eAAe,CAAC;IAC/C,KAAK,EAAE,eAAe;IA1DjC,MAAM,CAAC,UAAU,QAAuB;IACxC,MAAM,CAAC,kBAAkB,QAAuB;IAChD,MAAM,CAAC,cAAc,QAAuB;IAC5C,MAAM,CAAC,WAAW,kBAAe;IAEjC,MAAM,CAAC,KAAK,SAAK;IACjB,MAAM,KAAK,IAAI,IAAI,MAAM,CAExB;IAGD,SAAS,CAAC,SAAS,UAAQ;IAG3B,OAAO,CAAC,cAAc,CAAS;IAC/B,OAAO,CAAC,UAAU,CAAc;IAChC,OAAO,CAAC,cAAc,CAAwD;IAC9E,OAAO,CAAC,0BAA0B,CAA8C;IAChF,OAAO,CAAC,YAAY,CAAqD;IACzE,OAAO,CAAC,cAAc,CAAC,CAAO;IAC9B,OAAO,CAAC,kBAAkB,CAA4B;IACtD,OAAO,CAAC,uBAAuB,CAAS;IACxC,OAAO,CAAC,SAAS,CAAS;IAC1B,OAAO,CAAC,eAAe,CAAwB;IAG/C,MAAM,CAAC,mBAAmB,EAAE,iCAAiC,CAAC;IAC9D,MAAM,CAAC,wBAAwB,SAAM;IACrC,MAAM,CAAC,uBAAuB,EAAE,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,CAAgC;IACtF,MAAM,CAAC,oBAAoB,SAAsB;IACjD,MAAM,CAAC,eAAe,EAAE,IAAI,CAAoD;IAChF,MAAM,CAAC,SAAS,EAAE,IAAI,CAA6C;IAEnE,MAAM,CAAC,aAAa,EAAE,IAAI,CAAgD;IAC1E,MAAM,CAAC,aAAa,EAAE,aAAa,CAA2D;IAE9F,IAAW,kBAAkB,IAAI;QAAE,GAAG,EAAE,MAAM,CAAC;QAAC,SAAS,EAAE,SAAS,CAAA;KAAE,EAAE,CAQvE;IAED,IAAW,iBAAiB,IAAI,GAAG,CAAC,MAAM,CAAC,CAG1C;IAED,IAAW,WAAW,IAAI,IAAI,CAE7B;gBAGG,QAAQ,EAAE,IAAI,EACP,cAAc,EAAE,cAAc,CAAC,eAAe,CAAC,EAC/C,KAAK,EAAE,eAAe;IAWjC,SAAS;IAcT,gBAAgB,OAAc;IAC9B,KAAK,SAAqB;IAC1B,oBAAoB;IAkBpB,MAAM,CAAC,cAAc,EAAE,MAAM,CAAC,OAAO,GAAG,IAAI,CAAQ;IACpD,gBAAgB;IAmBhB,YAAY;IAoBZ,WAAW,CAAC,UAAU,UAAO;IAQ7B,eAAe,IAAI,GAAG,CAAC,MAAM,CAAC;IAqC9B,YAAY,CAAC,aAAa,GAAE,GAAG,CAAC,MAAM,CAAqB,GAAG,MAAM,EAAE;IA+CtE,oBAAoB;IAIpB,eAAe,CAAC,KAAK,EAAE,IAAI,GAAG,IAAI;IAiC3B,iBAAiB;IAQxB,MAAM,CAAC,SAAS,QAAa;IAC7B,MAAM,CAAC,oBAAoB,SAAe;IAC1C;;OAEG;IACH,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS;IAqB3D,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,KAAK,GAAE,KAA2C;IAMnI,MAAM,CAAC,gBAAgB,WAOrB;IAEF,MAAM,CAAC,cAAc,WASnB;IAEF,iBAAiB,CAAC,UAAU,EAAE,SAAS,EAAE;IAWzC,uBAAuB,CAAC,IAAI,EAAE,IAAI;IAOlC,SAAS,CAAC,MAAM;IAehB,SAAS,CAAC,MAAM;IAOhB,SAAS,CAAC,WAAW,CAAC,KAAK,EAAE,kBAAkB;IAK/C,SAAS,CAAC,WAAW,CAAC,KAAK,EAAE,kBAAkB;IAK/C,SAAS,CAAC,SAAS,CAAC,KAAK,EAAE,kBAAkB;IAK7C,IAAI,qBAAqB,IAAI,SAAS,EAAE,CAIvC;IAED,SAAS,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,EAAE;IAOtC,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;IAOnC,aAAa,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;IAO5C,OAAO,CAAC,yBAAyB;IAUjC,OAAO,CAAC,mBAAmB;IAe3B,yBAAyB,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI;IAmB9C,0BAA0B,CAAC,IAAI,EAAE,OAAO;IAQxC,8BAA8B,IAAI,SAAS,CAAC,IAAI,CAAC;IAgBjD,2BAA2B,IAAI,SAAS,CAAC,IAAI,CAAC;IAiB9C,UAAU,CAAC,IAAI,EAAE,IAAI;IAKrB,YAAY,CAAC,IAAI,EAAE,IAAI;IAKvB,oBAAoB;IAIpB,sBAAsB;IAItB,IAAI,IAAI,SAAS,EAAE;IAkBnB,mBAAmB,CAAC,KAAK,EAAE,gBAAgB,GAAG,IAAI;IAGlD,mBAAmB,CAAC,KAAK,EAAE,gBAAgB,GAAG,IAAI;IAGlD,iBAAiB,CAAC,KAAK,EAAE,gBAAgB,GAAG,IAAI;IAGhD,eAAe,IAAI,SAAS,CAAC,MAAM,CAAC;IAIpC,MAAM,CAAC,KAAK,EAAE,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI;IAMpC,SAAS,IAAI,IAAI;IAMjB,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI;IAMtC,WAAW,IAAI,IAAI;IAMnB,oBAAoB,IAAI,SAAS,CAAC,IAAI,CAAC;IAIvC,qBAAqB;IAKrB,eAAe;CAMlB;AAED,MAAM,WAAW,cAAc,CAAC,CAAC;IAC7B,IAAI,MAAM,IAAI,CAAC,CAAC;CACnB;AAED,cAAM,iCAAkC,YAAW,kBAAkB;IAEtD,UAAU,EAAE,oBAAoB;gBAAhC,UAAU,EAAE,oBAAoB;IAG3C,qBAAqB,CAAC,KAAK,EAAE,kBAAkB;IAa/C,mBAAmB,CAAC,KAAK,EAAE,kBAAkB;CAgBhD"}