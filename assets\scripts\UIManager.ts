import { _decorator, Component, Node, Label, Button } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('UIManager')
export class UIManager extends Component {
    @property(Label)
    public levelLabel: Label = null;
    
    @property(Label)
    public timeLabel: Label = null;
    
    @property(Button)
    public pauseButton: Button = null;

    private gameStartTime: number = 0;
    private currentLevel: number = 1;

    start() {
        this.gameStartTime = Date.now();
        this.updateLevelDisplay();
        
        // 绑定按钮事件
        this.pauseButton.node.on(Button.EventType.CLICK, this.onPauseClick, this);
    }

    update() {
        this.updateTimeDisplay();
    }

    updateLevelDisplay() {
        if (this.levelLabel) {
            this.levelLabel.string = `第 ${this.currentLevel} 关`;
        }
    }

    updateTimeDisplay() {
        if (this.timeLabel) {
            const elapsed = Math.floor((Date.now() - this.gameStartTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            this.timeLabel.string = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }

    onPauseClick() {
        // 暂停游戏逻辑
        console.log('游戏暂停');
    }

    setLevel(level: number) {
        this.currentLevel = level;
        this.updateLevelDisplay();
    }

    resetTimer() {
        this.gameStartTime = Date.now();
    }
}