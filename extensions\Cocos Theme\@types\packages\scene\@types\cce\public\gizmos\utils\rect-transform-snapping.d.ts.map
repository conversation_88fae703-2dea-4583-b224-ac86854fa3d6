{"version": 3, "file": "rect-transform-snapping.d.ts", "sourceRoot": "", "sources": ["../../../../../source/script/public/gizmos/utils/rect-transform-snapping.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,IAAI,EAAe,IAAI,EAAE,IAAI,EAAE,MAAM,IAAI,CAAC;AAIzD,UAAU,UAAU;IAChB,MAAM,EAAE,IAAI,CAAC;IACb,MAAM,EAAE,IAAI,CAAC;IACb,MAAM,EAAE,IAAI,CAAC;IACb,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;CAClB;AAED,UAAU,aAAa;IACnB,IAAI,EAAE,aAAa,EAAE,CAAC;IACtB,KAAK,EAAE,aAAa,EAAE,CAAC;IACvB,GAAG,EAAE,aAAa,EAAE,CAAC;IACrB,MAAM,EAAE,aAAa,EAAE,CAAA;CAC1B;AAED,UAAU,UAAU;IAChB,SAAS,EAAE,UAAU,CAAC;IACtB,QAAQ,EAAE,aAAa,CAAC;CAC3B;AAED,UAAU,aAAa;IACnB,OAAO,EAAE,MAAM,CAAC;IAChB,WAAW,EAAE,IAAI,CAAC;IAClB,WAAW,EAAE,IAAI,CAAC;IAClB,IAAI,EAAE,MAAM,CAAC;IACb,eAAe,CAAC,EAAE,UAAU,CAAC;CAChC;AAED,UAAU,mBAAmB;IACzB,cAAc,EAAE,OAAO,CAAC;IACxB,aAAa,EAAE,MAAM,CAAC;CACzB;AAED,cAAM,aAAa;IACf,KAAK,SAAC;IACN,YAAY,EAAE,IAAI,EAAE,CAAM;IAC1B,IAAI,EAAE,MAAK,CAAC,IAAI,CAAC,CAAC;IAClB,SAAS,CAAC,EAAE,IAAI,CAAC;gBAEL,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAK,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE;CAKjE;AAED,cAAM,kBAAkB;IACpB,iBAAiB,EAAE,aAAa,EAAE,CAAM;IACxC,UAAU,EAAE,GAAG,CAAC,MAAM,EAAE,aAAa,EAAE,CAAC,CAAa;IAErD,KAAK;IAIL,YAAY,CAAC,SAAS,EAAE,aAAa;IAWrC,gBAAgB,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM;CAuBnD;AAED,cAAM,qBAAsB,YAAW,mBAAmB;IACtD,cAAc,UAAQ;IACtB,kBAAkB,UAAQ;IAC1B,aAAa,SAAK;IAElB,uBAAuB,uBAAwD;IAC/E,cAAc,WAAe;IAC7B,cAAc,MAA4B;IAG1C,eAAe,MAA8B;IAC7C,yBAAyB,uBAAwD;IAGjF,UAAU,EAAE,UAAU,EAAE,CAAM;IAC9B,wBAAwB,EAAE,aAAa,EAAE,CAAM;IAG/C,YAAY,SAAO;IACnB,YAAY,SAAO;IACnB,SAAS,MAAiB;IAC1B,uBAAuB,uBAAwD;IAExE,iBAAiB,IAAI,mBAAmB;IAOxC,YAAY,CAAC,IAAI,EAAE,mBAAmB;IAK7C,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM;IAI5C,cAAc,CAAC,IAAI,EAAE,IAAI,GAAG,UAAU;IAetC,sBAAsB,CAAC,eAAe,EAAE,kBAAkB,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM;IAM3G,mBAAmB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM;IA4B/D,qBAAqB,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM;IAuCnF,0BAA0B;IAO1B,0BAA0B,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM;IAIxE,uBAAuB,CAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI;IAoBvE,wBAAwB,CAAC,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI;IAa1E,2BAA2B,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;IAwBxD,iBAAiB,CAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI;;;;;;;IAcjD,uBAAuB,CAAC,aAAa,EAAE,aAAa,EAAE,IAAI,EAAE,MAAK,CAAC,aAAa,CAAC,EAAE,SAAS,EAAE,MAAM;;;;;;IAwCnG,uBAAuB,CAAC,QAAQ,EAAE,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAK,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,MAAM;;;;;IAmDpG,qBAAqB,CAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI;IA8DrE,8BAA8B,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;IAoB3D,cAAc,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU;;;;IA8CnD,cAAc,CAAC,WAAW,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa;IAOrE,gBAAgB,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;IA0B7C,iBAAiB,CAAC,SAAS,EAAE,UAAU,GAAG,UAAU;IAYpD,iBAAiB,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,GAAG,aAAa,GAAC,IAAI;IAwE3E,2BAA2B;IAmB3B,0BAA0B;IAK1B,0BAA0B,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,MAAK,CAAC,IAAI,CAAC;IAI7E,2BAA2B,CAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI;IAgB3E,wBAAwB,CAAC,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI;IAgB1E,6BAA6B;IAwB7B,4BAA4B;IAK5B,gCAAgC,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM;IAI9E,6BAA6B,CAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI;CAqBhF;AAED,QAAA,MAAM,qBAAqB,uBAA8B,CAAC;AAE1D,OAAO,EACH,aAAa,EACb,kBAAkB,EAClB,qBAAqB,EACrB,mBAAmB,EACnB,qBAAqB,GACxB,CAAC"}